@echo off
set SOURCE="f:\2025\AIpile\AIpiles_final - updates"
set DEST="f:\2025\AIpile\further_M"

mkdir %DEST% >nul 2>&1

echo 正在复制核心脚本...
xcopy %SOURCE%\*.py %DEST%\ /E /C /H /Y /Q

echo 正在复制AI模型...
xcopy %SOURCE%\ai_models %DEST%\ai_models\ /E /C /H /Y /Q

echo 正在复制训练数据...
xcopy %SOURCE%\training_data %DEST%\training_data\ /E /C /H /Y /Q

echo 正在复制配置文件...
xcopy %SOURCE%\*.json %DEST%\ /C /H /Y /Q
xcopy %SOURCE%\*.txt %DEST%\ /C /H /Y /Q

echo 正在迁移虚拟环境...
xcopy %SOURCE%\ai_pile_env %DEST%\ai_pile_env\ /E /C /H /Y /Q

echo 复制完成！校验文件完整性...
dir %DEST% /s
pause