#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能合成数据生成器 - GUI版本
基于机器学习和GZ传统方法的智能桩身完整性数据生成器
集成了信号增强技术（加噪、缩放）以提高数据真实性
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import threading
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import seaborn as sns # 虽然导入了，但在此版本脚本中未直接使用seaborn绘图，plt已满足需求
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class IntelligentSyntheticDataGenerator:
    """智能合成数据生成器核心类"""

    def __init__(self):
        """初始化生成器"""
        self.learned_distributions = {}
        self.feature_scalers = {}
        self.gz_calculator = GZMethodCalculator()
        self.data_stats = {}

        # 新增：信号增强相关参数
        self.apply_augmentation = True  # 默认启用增强
        self.noise_std_s = 1.5          # 速度信号噪声标准差
        self.noise_std_a = 0.3          # 幅度信号噪声标准差
        self.scale_factor_s_min = 0.95  # 速度信号最小缩放因子
        self.scale_factor_s_max = 1.05  # 速度信号最大缩放因子
        self.scale_factor_a_min = 0.90  # 幅度信号最小缩放因子
        self.scale_factor_a_max = 1.10  # 幅度信号最大缩放因子

    def learn_from_existing_data(self, data_directory: str, progress_callback=None) -> Dict:
        """从现有数据中学习特征分布"""
        learning_results = {
            'total_samples': 0,
            'class_distributions': {},
            'feature_statistics': {},
            'learning_status': 'success'
        }

        try:
            pile_classes = ['I', 'II', 'III', 'IV']
            total_files = 0
            processed_files = 0

            # 统计总文件数
            for pile_class in pile_classes:
                class_dir = os.path.join(data_directory, pile_class)
                if os.path.exists(class_dir):
                    total_files += len([f for f in os.listdir(class_dir) if f.endswith('.txt')])
            
            if total_files == 0:
                learning_results['learning_status'] = 'error: 指定目录中未找到任何 .txt 文件'
                return learning_results

            for pile_class in pile_classes:
                class_dir = os.path.join(data_directory, pile_class)
                if not os.path.exists(class_dir):
                    print(f"警告: 目录 {class_dir} 不存在，跳过 {pile_class} 类桩。")
                    continue

                class_data = []
                class_files = [f for f in os.listdir(class_dir) if f.endswith('.txt')]
                if not class_files:
                    print(f"信息: 目录 {class_dir} 中没有 .txt 文件，跳过 {pile_class} 类桩。")
                    continue

                for filename in class_files:
                    try:
                        file_path = os.path.join(class_dir, filename)
                        df = pd.read_csv(file_path, sep='\t')

                        if len(df.columns) >= 7:
                            # 标准化列名，确保一致性
                            df.columns = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3'] + list(df.columns[7:]) 
                            # 只取前7列我们关心的
                            class_data.append(df[['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']])
                        else:
                            print(f"文件 {filename} 列数不足7列，已跳过。实际列数: {len(df.columns)}")
                            # 即使文件被跳过，也应该在进度中体现
                            if total_files > 0: # 避免除以零
                                processed_files += 1
                                if progress_callback:
                                    progress = (processed_files / total_files) * 50  # 学习阶段占50%
                                    progress_callback(progress, f"学习 {pile_class}类桩数据: {filename} (列数不足，跳过)")
                            continue


                        processed_files += 1
                        if progress_callback and total_files > 0:
                            progress = (processed_files / total_files) * 50
                            progress_callback(progress, f"学习 {pile_class}类桩数据: {filename}")

                    except Exception as e:
                        print(f"读取文件 {filename} 失败: {e}")
                        # 即使文件读取失败，也应该在进度中体现
                        if total_files > 0: # 避免除以零
                            processed_files += 1
                            if progress_callback:
                                progress = (processed_files / total_files) * 50
                                progress_callback(progress, f"学习 {pile_class}类桩数据: {filename} (读取失败)")
                        continue
                
                if not class_data: # 如果该类别没有成功读取到任何数据
                    print(f"警告: {pile_class} 类桩没有成功加载任何数据。")
                    learning_results['class_distributions'][f"{pile_class}类桩"] = {
                        'sample_count': 0,
                        'total_points': 0,
                        'feature_means': {}, # 或者设为None/NaN
                        'feature_stds': {}
                    }
                    continue


                if class_data:
                    combined_data = pd.concat(class_data, ignore_index=True)
                    if combined_data.empty:
                        print(f"警告: {pile_class} 类桩合并数据后为空。")
                        continue

                    features = self._extract_features(combined_data)
                    if features.empty:
                        print(f"警告: 从 {pile_class} 类桩数据中提取特征后为空。")
                        continue
                    
                    self._learn_class_distribution(f"{pile_class}类桩", features)

                    learning_results['class_distributions'][f"{pile_class}类桩"] = {
                        'sample_count': len(class_data), # 文件数
                        'total_points': len(combined_data), # 总数据点数
                        'feature_means': features.mean().to_dict(),
                        'feature_stds': features.std().to_dict()
                    }
                    learning_results['total_samples'] += len(class_data)
            
            if learning_results['total_samples'] == 0 and learning_results['learning_status'] == 'success':
                 learning_results['learning_status'] = 'error: 未能从任何类别中学习到数据。请检查数据文件和目录结构。'


            return learning_results

        except Exception as e:
            learning_results['learning_status'] = f'error: {str(e)}'
            return learning_results

    def _extract_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """从原始数据中提取特征"""
        features = []
        if df.empty or not all(col in df.columns for col in ['S1', 'A1', 'S2', 'A2', 'S3', 'A3', 'Depth']):
            return pd.DataFrame() # 返回空DataFrame如果输入不满足条件

        for _, row in df.iterrows():
            s_values = [row['S1'], row['S2'], row['S3']]
            a_values = [row['A1'], row['A2'], row['A3']]
            
            # 过滤掉NaN或inf值，避免影响统计计算
            s_values_clean = [x for x in s_values if pd.notna(x) and np.isfinite(x)]
            a_values_clean = [x for x in a_values if pd.notna(x) and np.isfinite(x)]

            feature_row = {
                'S1': row['S1'], 'A1': row['A1'],
                'S2': row['S2'], 'A2': row['A2'],
                'S3': row['S3'], 'A3': row['A3'],
                'Depth': row['Depth'],
                
                'Speed_Mean': np.mean(s_values_clean) if s_values_clean else np.nan,
                'Speed_Std': np.std(s_values_clean) if len(s_values_clean) > 1 else 0, # std需要至少两个点
                'Amp_Mean': np.mean(a_values_clean) if a_values_clean else np.nan,
                'Amp_Std': np.std(a_values_clean) if len(a_values_clean) > 1 else 0,
                
                'Speed_Range': (max(s_values_clean) - min(s_values_clean)) if s_values_clean else np.nan,
                'Amp_Range': (max(a_values_clean) - min(a_values_clean)) if a_values_clean else np.nan,
            }
            features.append(feature_row)
        
        # 清理包含NaN的特征行
        features_df = pd.DataFrame(features).dropna()
        return features_df


    def _learn_class_distribution(self, pile_class: str, features: pd.DataFrame):
        """学习特定类别的特征分布"""
        if features.empty:
            print(f"警告: 为 {pile_class} 提供的特征数据为空，无法学习分布。")
            self.learned_distributions[pile_class] = None # 标记为无法学习
            self.feature_scalers[pile_class] = None
            self.data_stats[pile_class] = {
                'feature_names': [],
                'original_stats': {}
            }
            return

        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(features)

        # 动态选择组件数，确保不超过样本数
        n_samples = len(features)
        if n_samples < 2: # GMM至少需要一些样本
             print(f"警告: {pile_class} 样本数过少 ({n_samples})，无法训练GMM。")
             self.learned_distributions[pile_class] = None
             self.feature_scalers[pile_class] = scaler # Scaler可能仍然有用
             self.data_stats[pile_class] = {
                'feature_names': features.columns.tolist(),
                'original_stats': features.describe().to_dict()
             }
             return

        n_components = min(3, max(1, n_samples // 10)) # 至少1个组件
        try:
            gmm = GaussianMixture(n_components=n_components, random_state=42, covariance_type='diag')
            gmm.fit(scaled_features)
            self.learned_distributions[pile_class] = gmm
        except ValueError as e:
            print(f"为 {pile_class} 训练GMM失败: {e}. 可能样本量过少或特征存在问题。")
            self.learned_distributions[pile_class] = None # 标记为训练失败
            
        self.feature_scalers[pile_class] = scaler
        self.data_stats[pile_class] = {
            'feature_names': features.columns.tolist(),
            'original_stats': features.describe().to_dict()
        }


    def generate_intelligent_data(self, pile_class: str, num_samples: int,
                                progress_callback=None) -> pd.DataFrame:
        """生成智能合成数据"""
        if pile_class not in self.learned_distributions or self.learned_distributions[pile_class] is None:
            raise ValueError(f"未学习到 {pile_class} 的分布信息或学习失败。")

        gmm = self.learned_distributions[pile_class]
        scaler = self.feature_scalers[pile_class]
        
        if pile_class not in self.data_stats or not self.data_stats[pile_class]['feature_names']:
             raise ValueError(f"数据统计信息不完整，无法为 {pile_class} 生成数据。")
        feature_names = self.data_stats[pile_class]['feature_names']


        generated_data = []

        for i in range(num_samples):
            sample_scaled, _ = gmm.sample(1)
            sample = scaler.inverse_transform(sample_scaled)[0]
            feature_dict = dict(zip(feature_names, sample))
            optimized_features = self._apply_gz_constraints(feature_dict, pile_class)
            depth_data = self._generate_depth_sequence(optimized_features, pile_class)
            generated_data.extend(depth_data)

            if progress_callback and i % (num_samples // 10 if num_samples >=10 else 1) == 0 : # 更频繁的回调
                progress = 50 + ((i + 1) / num_samples) * 50
                progress_callback(progress, f"生成 {pile_class} 样本 {i+1}/{num_samples}")
        
        if not generated_data:
            return pd.DataFrame() # 返回空DataFrame如果未生成任何数据
        return pd.DataFrame(generated_data)

    def _apply_gz_constraints(self, features: Dict, pile_class: str) -> Dict:
        """应用GZ方法约束优化特征"""
        gz_ranges = {
            'I类桩': {'sp_range': (95, 110), 'ad_range': (-2, 2)},
            'II类桩': {'sp_range': (85, 100), 'ad_range': (0, 6)},
            'III类桩': {'sp_range': (70, 85), 'ad_range': (4, 10)},
            'IV类桩': {'sp_range': (50, 75), 'ad_range': (8, 15)}
        }

        if pile_class in gz_ranges:
            ranges = gz_ranges[pile_class]
            for speed_key in ['S1', 'S2', 'S3', 'Speed_Mean']: # 也约束一下均值
                if speed_key in features:
                    sp_min, sp_max = ranges['sp_range']
                    features[speed_key] = np.clip(features[speed_key], sp_min, sp_max)
            for amp_key in ['A1', 'A2', 'A3', 'Amp_Mean']: # 也约束一下均值
                if amp_key in features:
                    ad_min, ad_max = ranges['ad_range']
                    features[amp_key] = np.clip(features[amp_key], ad_min, ad_max)
        return features

    def _generate_depth_sequence(self, base_features: Dict, pile_class: str) -> List[Dict]:
        """基于基础特征生成深度序列数据，并应用信号增强"""
        num_depths = np.random.randint(80, 150)
        depths = np.linspace(min(1, base_features.get('Depth', 10) * 0.5), # 起始深度基于GMM采样
                             base_features.get('Depth', 30) * 1.5, # 结束深度也基于GMM采样
                             num_depths)
        depths = np.clip(depths, 1, 50) # 确保深度在合理范围

        sequence_data = []

        # 使用GMM采样得到的S/A值作为该序列的基础值
        s1_base = base_features.get('S1', 100)
        a1_base = base_features.get('A1', 0)
        s2_base = base_features.get('S2', 100)
        a2_base = base_features.get('A2', 0)
        s3_base = base_features.get('S3', 100)
        a3_base = base_features.get('A3', 0)

        for depth in depths:
            depth_factor = 1 + 0.05 * np.sin(depth * np.random.uniform(0.3,0.7)) * np.random.normal(0, 0.1) # 更平滑的变化

            row = {
                'Depth': depth,
                'S1': s1_base * depth_factor + np.random.normal(0, 1.0), # 减小基础随机性
                'A1': a1_base + np.random.normal(0, 0.2),           # 减小基础随机性
                'S2': s2_base * depth_factor + np.random.normal(0, 1.0),
                'A2': a2_base + np.random.normal(0, 0.2),
                'S3': s3_base * depth_factor + np.random.normal(0, 1.0),
                'A3': a3_base + np.random.normal(0, 0.2),
            }

            # --- 应用信号增强技术 ---
            if self.apply_augmentation:
                # 1. 添加高斯噪声
                row['S1'] += np.random.normal(0, self.noise_std_s)
                row['A1'] += np.random.normal(0, self.noise_std_a)
                row['S2'] += np.random.normal(0, self.noise_std_s)
                row['A2'] += np.random.normal(0, self.noise_std_a)
                row['S3'] += np.random.normal(0, self.noise_std_s)
                row['A3'] += np.random.normal(0, self.noise_std_a)

                # 2. 随机缩放
                row['S1'] *= np.random.uniform(self.scale_factor_s_min, self.scale_factor_s_max)
                row['A1'] *= np.random.uniform(self.scale_factor_a_min, self.scale_factor_a_max)
                row['S2'] *= np.random.uniform(self.scale_factor_s_min, self.scale_factor_s_max)
                row['A2'] *= np.random.uniform(self.scale_factor_a_min, self.scale_factor_a_max)
                row['S3'] *= np.random.uniform(self.scale_factor_s_min, self.scale_factor_s_max)
                row['A3'] *= np.random.uniform(self.scale_factor_a_min, self.scale_factor_a_max)
            # --- 信号增强结束 ---

            # 确保参数在合理范围内 (应用GZ约束后再次裁剪，或在此处进行最终裁剪)
            # GZ约束中已经包含了一些范围，这里可以作为最终保障
            gz_ranges_specific = { # 用于最终裁剪的更宽松的范围，确保增强后的数据不会太离谱
                'I类桩': {'sp_range': (70, 130), 'ad_range': (-5, 5)},
                'II类桩': {'sp_range': (60, 120), 'ad_range': (-2, 10)},
                'III类桩': {'sp_range': (50, 100), 'ad_range': (0, 15)},
                'IV类桩': {'sp_range': (30, 90), 'ad_range': (5, 20)}
            }
            current_pile_ranges = gz_ranges_specific.get(pile_class, {'sp_range': (30,150), 'ad_range': (-5,25)})


            row['S1'] = np.clip(row['S1'], current_pile_ranges['sp_range'][0], current_pile_ranges['sp_range'][1])
            row['S2'] = np.clip(row['S2'], current_pile_ranges['sp_range'][0], current_pile_ranges['sp_range'][1])
            row['S3'] = np.clip(row['S3'], current_pile_ranges['sp_range'][0], current_pile_ranges['sp_range'][1])
            row['A1'] = np.clip(row['A1'], current_pile_ranges['ad_range'][0], current_pile_ranges['ad_range'][1])
            row['A2'] = np.clip(row['A2'], current_pile_ranges['ad_range'][0], current_pile_ranges['ad_range'][1])
            row['A3'] = np.clip(row['A3'], current_pile_ranges['ad_range'][0], current_pile_ranges['ad_range'][1])

            sequence_data.append(row)
        return sequence_data


class GZMethodCalculator:
    """GZ方法计算器"""
    def calculate_I_ji(self, Sp: float, Ad: float, Bi_ratio: float = 1.0) -> int:
        """计算I(j,i)值 - 根据标准定义调整"""
        # 确保Sp, Ad是有效数值
        if not (pd.notna(Sp) and pd.notna(Ad) and np.isfinite(Sp) and np.isfinite(Ad)):
            return 4 # 无效数据视为最差

        # I(j,i) = 1 (完整)
        if Bi_ratio >= 0.8 and ((Sp >= 95 and -2.0 <= Ad <= 2.0) or \
                              (85 <= Sp < 95 and -2.0 <= Ad <= 0.0)): # 标准中对I类桩的描述
            return 1
        
        # I(j,i) = 2 (轻微缺陷)
        if (0.6 <= Bi_ratio < 0.8 and 80 <= Sp < 95 and 0.0 < Ad <= 5.0) or \
           (Bi_ratio >= 0.6 and 70 <= Sp < 80 and -2.0 <= Ad <= 5.0) or \
           (Bi_ratio >= 0.6 and Sp >= 80 and 2.0 < Ad <= 6.0):
            return 2

        # I(j,i) = 3 (明显缺陷)
        if (0.4 <= Bi_ratio < 0.6 and 65 <= Sp < 80 and 2.0 < Ad <= 8.0) or \
           (Bi_ratio >= 0.4 and 50 <= Sp < 65 and 0.0 <= Ad <= 8.0) or \
           (Bi_ratio >= 0.4 and Sp >= 65 and 5.0 < Ad <= 10.0):
            return 3
            
        # I(j,i) = 4 (严重缺陷)
        # 所有其他情况，或更严格的缺陷定义
        return 4


    def verify_generated_data(self, df: pd.DataFrame) -> str:
        """验证生成的数据符合哪个桩类"""
        if df.empty:
            return "未定类别 (无数据)"
            
        K_values = []
        for _, row in df.iterrows():
            I_ji_values = []
            # 假设Bi_ratio为1，实际应用中可能需要从数据中获取或估计
            Bi_ratio = 1.0 
            
            # 检查S, A值是否存在且有效
            s_cols = [f'S{i+1}' for i in range(3)]
            a_cols = [f'A{i+1}' for i in range(3)]

            valid_row = True
            for s_col, a_col in zip(s_cols, a_cols):
                if not (s_col in row and a_col in row and \
                        pd.notna(row[s_col]) and pd.notna(row[a_col]) and \
                        np.isfinite(row[s_col]) and np.isfinite(row[a_col])):
                    valid_row = False
                    break
            if not valid_row:
                K_values.append(4) # 无效数据点按最差处理
                continue

            for profile_idx in range(3):
                sp = row[f'S{profile_idx+1}']
                ad = row[f'A{profile_idx+1}']
                I_ji = self.calculate_I_ji(sp, ad, Bi_ratio)
                I_ji_values.append(I_ji)

            if I_ji_values: # 确保列表不为空
                sum_I_ji_sq = sum(i**2 for i in I_ji_values)
                sum_I_ji = sum(I_ji_values)
                K_i = int((sum_I_ji_sq / sum_I_ji) + 0.5) if sum_I_ji > 0 else 4 # 防止除零,默认为4
                K_values.append(K_i)
            else:
                K_values.append(4) # 如果没有有效的I_ji值，也视为严重缺陷

        if not K_values: # 如果整个DataFrame没有有效的K值
             return "未定类别 (无有效K值)"

        # 根据K值分布确定桩类 (简化规则)
        # 统计各类K值的数量
        k_counts = {k: K_values.count(k) for k in set(K_values)}
        
        if 4 in k_counts and k_counts[4] > 0: # 只要有一个K=4的点
            return "IV类桩"
        elif 3 in k_counts and k_counts[3] > 0: # 有K=3的点
            return "III类桩"
        elif 2 in k_counts and k_counts[2] > 0: # 有K=2的点
            return "II类桩"
        elif all(k == 1 for k in K_values): # 所有点都是K=1
            return "I类桩"
        else: # 其他复杂情况或数据不足以明确分类
            # 可以根据K值的平均值或主要分布来尝试判断，但这里简化处理
            if K_values: # 确保K_values不为空
                avg_k = np.mean(K_values)
                if avg_k >= 3.5: return "IV类桩 (趋势)"
                if avg_k >= 2.5: return "III类桩 (趋势)"
                if avg_k >= 1.5: return "II类桩 (趋势)"
            return "未定类别 (混合或无法判断)"


class IntelligentDataGeneratorGUI:
    """智能合成数据生成器GUI界面"""

    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.generator = IntelligentSyntheticDataGenerator()
        self.data_directory = ""
        self.learning_results = {}

        self.apply_augmentation_var = tk.BooleanVar(value=self.generator.apply_augmentation)

        self.setup_gui()
        self.setup_styles()

    def setup_gui(self):
        """设置GUI界面"""
        self.root.title("🧠 智能桩身完整性数据生成器 v2.1 (含信号增强)")
        self.root.geometry("1200x850") # 稍微增加高度以容纳新控件
        self.root.configure(bg='#f0f0f0')

        try:
            # 尝试设置程序图标 (确保 'icon.ico' 文件存在于脚本同目录下或提供完整路径)
            # self.root.iconbitmap('icon.ico') 
            pass # 如果没有图标文件，静默处理
        except tk.TclError:
            print("未能加载图标文件 'icon.ico'.")


        self.create_main_frame()
        self.create_control_panel()
        self.create_progress_panel()
        self.create_results_panel() # 信号增强选框将在此处添加
        self.create_visualization_panel()

    def setup_styles(self):
        """设置样式"""
        self.style = ttk.Style()
        try:
            self.style.theme_use('clam') # 'clam', 'alt', 'default', 'classic'
        except tk.TclError:
            print("Clam theme not available, using default.")
            self.style.theme_use('default')


        self.style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground='#2c3e50')
        self.style.configure('Subtitle.TLabel', font=('Arial', 12, 'bold'), foreground='#34495e')
        self.style.configure('Info.TLabel', font=('Arial', 10), foreground='#7f8c8d')
        self.style.configure('Success.TLabel', font=('Arial', 10), foreground='#27ae60')
        self.style.configure('Error.TLabel', font=('Arial', 10), foreground='#e74c3c')
        self.style.configure('Primary.TButton', font=('Arial', 10, 'bold'), padding=5)
        self.style.configure('Success.TButton', font=('Arial', 10, 'bold'), background='#2ecc71', foreground='white', padding=5)
        self.style.map('Success.TButton', background=[('active', '#27ae60')])
        self.style.configure('Warning.TButton', font=('Arial', 10, 'bold'), background='#e67e22', foreground='white', padding=5)
        self.style.map('Warning.TButton', background=[('active', '#d35400')])
        self.style.configure('Accent.TButton', font=('Arial', 11, 'bold'), background='#e74c3c', foreground='white', padding=6) # For Generate button
        self.style.map('Accent.TButton', background=[('active', '#c0392b')])
        self.style.configure('Purple.TButton', font=('Arial', 11, 'bold'), background='#9b59b6', foreground='white', padding=6) # For Generate All button
        self.style.map('Purple.TButton', background=[('active', '#8e44ad')])
        self.style.configure('Browse.TButton', font=('Arial', 10, 'bold'), background='#3498db', foreground='white', padding=(10,5))
        self.style.map('Browse.TButton', background=[('active', '#2980b9')])
        self.style.configure('Learn.TButton', font=('Arial', 12, 'bold'), background='#27ae60', foreground='white', padding=(15,8))
        self.style.map('Learn.TButton', background=[('active', '#229954')])


    def create_main_frame(self):
        """创建主框架"""
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="🧠 智能桩身完整性数据生成器",
                              font=('Arial', 20, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(pady=(10,0), expand=True)

        subtitle_label = tk.Label(title_frame, text="基于机器学习和GZ传统方法的智能数据生成 (v2.1 含信号增强)",
                                 font=('Arial', 12), fg='#bdc3c7', bg='#2c3e50')
        subtitle_label.pack(pady=(0,10))

        self.main_frame = tk.Frame(self.root, bg='#f0f0f0')
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)

    def create_control_panel(self):
        """创建控制面板"""
        control_frame = tk.LabelFrame(self.main_frame, text="📁 数据源配置",
                                     font=('Arial', 12, 'bold'), bg='#f0f0f0', fg='#2c3e50', padx=10, pady=10)
        control_frame.pack(fill='x', pady=(0, 10))

        dir_frame = tk.Frame(control_frame, bg='#f0f0f0')
        dir_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(dir_frame, text="训练数据目录:", font=('Arial', 10, 'bold'),
                bg='#f0f0f0', fg='#2c3e50').pack(side='left', padx=(0,5))

        self.dir_var = tk.StringVar()
        self.dir_entry = ttk.Entry(dir_frame, textvariable=self.dir_var, font=('Arial', 10),
                                 width=60, state='readonly')
        self.dir_entry.pack(side='left', padx=(0, 5), fill='x', expand=True)

        self.browse_btn = ttk.Button(dir_frame, text="📂 浏览", style='Browse.TButton',
                                   command=self.browse_directory)
        self.browse_btn.pack(side='right')

        learn_frame = tk.Frame(control_frame, bg='#f0f0f0')
        learn_frame.pack(fill='x', padx=10, pady=(5,0))

        self.learn_btn = ttk.Button(learn_frame, text="🧠 开始学习数据分布", style='Learn.TButton',
                                  command=self.start_learning)
        self.learn_btn.pack(side='left')

        self.learn_status = ttk.Label(learn_frame, text="请先选择数据目录", style='Info.TLabel',
                                    font=('Arial', 10, 'italic'))
        self.learn_status.pack(side='left', padx=(20, 0), pady=(5,0))

    def create_progress_panel(self):
        """创建进度面板"""
        progress_frame = tk.LabelFrame(self.main_frame, text="📊 处理进度",
                                      font=('Arial', 12, 'bold'), bg='#f0f0f0', fg='#2c3e50', padx=10, pady=10)
        progress_frame.pack(fill='x', pady=(0, 10))

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                           maximum=100, length=400, mode='determinate')
        self.progress_bar.pack(padx=10, pady=10, fill='x', expand=True)

        self.progress_text = ttk.Label(progress_frame, text="等待开始...", style='Info.TLabel',
                                     font=('Arial', 10))
        self.progress_text.pack(pady=(0, 10))

    def create_results_panel(self):
        """创建结果面板"""
        results_frame = tk.LabelFrame(self.main_frame, text="📈 学习结果与数据生成",
                                     font=('Arial', 12, 'bold'), bg='#f0f0f0', fg='#2c3e50', padx=10, pady=10)
        results_frame.pack(fill='x', pady=(0, 10))

        results_info_frame = tk.Frame(results_frame, bg='#f0f0f0')
        results_info_frame.pack(fill='x', padx=10, pady=5)

        self.results_text = tk.Text(results_info_frame, height=6, width=80,
                                   font=('Consolas', 9), bg='#ffffff', fg='#2c3e50',
                                   relief='sunken', bd=1, wrap=tk.WORD)
        self.results_text.pack(side='left', fill='both', expand=True)
        
        scrollbar = ttk.Scrollbar(results_info_frame, orient='vertical', command=self.results_text.yview)
        scrollbar.pack(side='right', fill='y')
        self.results_text.config(yscrollcommand=scrollbar.set)

        generate_controls_frame = tk.Frame(results_frame, bg='#f0f0f0')
        generate_controls_frame.pack(fill='x', padx=10, pady=(5,0))


        # 数据生成控制
        generate_frame = tk.Frame(generate_controls_frame, bg='#f0f0f0')
        generate_frame.pack(side='left', fill='x', expand=True)


        tk.Label(generate_frame, text="桩类:", font=('Arial', 10, 'bold'),
                bg='#f0f0f0', fg='#2c3e50').pack(side='left', padx=(0,5), pady=5)
        self.pile_class_var = tk.StringVar(value="I类桩")
        pile_class_combo = ttk.Combobox(generate_frame, textvariable=self.pile_class_var,
                                       values=["I类桩", "II类桩", "III类桩", "IV类桩"],
                                       state='readonly', width=10, font=('Arial', 10))
        pile_class_combo.pack(side='left', padx=(0, 15), pady=5)

        tk.Label(generate_frame, text="样本数量:", font=('Arial', 10, 'bold'),
                bg='#f0f0f0', fg='#2c3e50').pack(side='left', padx=(0,5), pady=5)
        self.sample_count_var = tk.StringVar(value="50")
        sample_count_entry = ttk.Entry(generate_frame, textvariable=self.sample_count_var,
                                     width=8, font=('Arial', 10))
        sample_count_entry.pack(side='left', padx=(0,15), pady=5)
        
        # 新增：信号增强复选框
        self.augmentation_check = ttk.Checkbutton(
            generate_frame, # 放在同一行
            text="启用信号增强",
            variable=self.apply_augmentation_var,
            style='Info.TLabel', # 使用TLabel的样式，使其看起来像标签
            command=self.toggle_augmentation
        )
        self.augmentation_check.pack(side='left', padx=(10, 0), pady=5)


        buttons_frame = tk.Frame(results_frame, bg='#f0f0f0')
        buttons_frame.pack(fill='x', padx=10, pady=(5,10))

        self.generate_btn = ttk.Button(buttons_frame, text="🚀 生成选中类别", style='Accent.TButton',
                                     command=self.start_generation, state='disabled')
        self.generate_btn.pack(side='left', padx=(0, 10), fill='x', expand=True)

        self.generate_all_btn = ttk.Button(buttons_frame, text="🎯 生成全部类别", style='Purple.TButton',
                                         command=self.start_generation_all, state='disabled')
        self.generate_all_btn.pack(side='left', fill='x', expand=True)


    def toggle_augmentation(self):
        """切换信号增强的启用状态"""
        self.generator.apply_augmentation = self.apply_augmentation_var.get()
        status_text = "启用" if self.generator.apply_augmentation else "禁用"
        self.progress_text.config(text=f"信号增强已{status_text}。") # 在进度条下显示状态
        # print(f"信号增强 {status_text}") # 调试信息


    def create_visualization_panel(self):
        """创建可视化面板"""
        viz_frame = tk.LabelFrame(self.main_frame, text="📊 数据分布可视化",
                                 font=('Arial', 12, 'bold'), bg='#f0f0f0', fg='#2c3e50', padx=10, pady=10)
        viz_frame.pack(fill='both', expand=True)

        self.fig, self.axes = plt.subplots(2, 2, figsize=(10, 7)) # 调整figsize以适应GUI
        self.fig.patch.set_facecolor('#f0f0f0')
        plt.subplots_adjust(hspace=0.4, wspace=0.3) # 调整子图间距

        self.canvas = FigureCanvasTkAgg(self.fig, viz_frame)
        self.canvas_widget = self.canvas.get_tk_widget()
        self.canvas_widget.pack(fill='both', expand=True, padx=5, pady=5)

        self.clear_plots()

    def clear_plots(self):
        """清空图表"""
        plot_titles = [
            '各类桩样本文件数量', '各类桩总数据点数量',
            '各类桩平均波速(%)', '各类桩平均波幅(dB)'
        ]
        for i, ax in enumerate(self.axes.flat):
            ax.clear()
            ax.set_title(plot_titles[i] if i < len(plot_titles) else "等待数据...", fontsize=10)
            ax.grid(True, linestyle='--', alpha=0.6)
            ax.tick_params(axis='x', labelsize=8)
            ax.tick_params(axis='y', labelsize=8)
        self.fig.tight_layout(pad=2.0) # 增加padding
        self.canvas.draw_idle()


    def browse_directory(self):
        """浏览目录"""
        directory = filedialog.askdirectory(title="选择包含I、II、III、IV类桩数据的根目录")
        if directory:
            self.data_directory = directory
            self.dir_var.set(directory)
            self.learn_status.config(text="目录已选择，点击开始学习", style='Success.TLabel')
            self.check_directory_structure()

    def check_directory_structure(self):
        """检查目录结构"""
        if not self.data_directory:
            return
        required_dirs = ['I', 'II', 'III', 'IV']
        missing_dirs = []
        found_txt_files = False

        for dir_name in required_dirs:
            class_dir_path = os.path.join(self.data_directory, dir_name)
            if not os.path.exists(class_dir_path) or not os.path.isdir(class_dir_path):
                missing_dirs.append(dir_name)
            else:
                # 检查该子目录中是否有txt文件
                if any(f.endswith('.txt') for f in os.listdir(class_dir_path)):
                    found_txt_files = True
        
        if missing_dirs:
            self.learn_status.config(text=f"警告: 缺少或非法的子目录: {', '.join(missing_dirs)}", style='Error.TLabel')
        elif not found_txt_files:
            self.learn_status.config(text="警告: 子目录中未找到任何 .txt 数据文件。", style='Error.TLabel')
        else:
            self.learn_status.config(text="目录结构正确，可以开始学习。", style='Success.TLabel')


    def start_learning(self):
        """开始学习数据分布"""
        if not self.data_directory:
            messagebox.showerror("错误", "请先选择数据目录")
            return
        
        # 再次检查目录结构和文件
        self.check_directory_structure()
        if "警告" in self.learn_status.cget("text"):
             messagebox.showwarning("目录问题", "数据目录结构或文件存在问题，请检查后再学习。")
             return


        self.learn_btn.config(state='disabled')
        self.browse_btn.config(state='disabled')
        self.generate_btn.config(state='disabled')
        self.generate_all_btn.config(state='disabled')

        self.progress_var.set(0)
        self.progress_text.config(text="开始学习数据分布...", style='Info.TLabel')
        self.results_text.delete(1.0, tk.END) # 清空上次结果

        thread = threading.Thread(target=self.learning_worker)
        thread.daemon = True
        thread.start()

    def learning_worker(self):
        """学习工作线程"""
        try:
            self.learning_results = self.generator.learn_from_existing_data(
                self.data_directory, self.update_progress)
            self.root.after(0, self.learning_completed)
        except Exception as e:
            self.root.after(0, lambda: self.learning_error(str(e)))

    def update_progress(self, progress: float, message: str):
        """更新进度"""
        self.root.after(0, lambda: self._update_progress_ui(progress, message))

    def _update_progress_ui(self, progress: float, message: str):
        """更新进度UI"""
        self.progress_var.set(progress)
        self.progress_text.config(text=message)
        self.root.update_idletasks() # 确保UI实时更新

    def learning_completed(self):
        """学习完成"""
        self.learn_btn.config(state='normal')
        self.browse_btn.config(state='normal')

        self.display_learning_results() # 先显示结果
        
        if self.learning_results.get('learning_status') == 'success' and self.learning_results.get('total_samples', 0) > 0 :
            self.progress_var.set(100)
            self.progress_text.config(text="学习完成！可以开始生成数据。", style='Success.TLabel')
            self.generate_btn.config(state='normal')
            self.generate_all_btn.config(state='normal')
            self.update_visualization()
        elif self.learning_results.get('total_samples', 0) == 0 and self.learning_results.get('learning_status') == 'success':
            self.progress_text.config(text="学习过程结束，但未能从任何类别加载有效数据。", style='Error.TLabel')
            messagebox.showwarning("学习结果", "学习过程已完成，但未能从任何类别中加载有效数据点。请检查数据文件内容和格式。")
        else:
            self.progress_text.config(text=f"学习失败: {self.learning_results.get('learning_status', '未知错误')}", style='Error.TLabel')
            messagebox.showerror("学习失败", f"学习过程中出现错误:\n{self.learning_results.get('learning_status', '未知错误')}")
            self.clear_plots() # 清空图表以防显示旧数据

    def learning_error(self, error_message: str):
        """学习出错"""
        self.learn_btn.config(state='normal')
        self.browse_btn.config(state='normal')
        self.progress_text.config(text=f"学习失败: {error_message}", style='Error.TLabel')
        messagebox.showerror("学习失败", f"学习过程中出现严重错误:\n{error_message}")
        self.clear_plots()

    def display_learning_results(self):
        """显示学习结果"""
        self.results_text.delete(1.0, tk.END)
        results_str = ""
        if self.learning_results:
            status = self.learning_results.get('learning_status', '未知状态')
            if status == 'success':
                results_str += f"🎉 学习完成！\n"
                results_str += f"📊 总有效样本文件数: {self.learning_results.get('total_samples', 0)}\n\n"
                
                distributions = self.learning_results.get('class_distributions', {})
                if not distributions:
                     results_str += "未能从任何类别中学习到分布信息。\n"
                
                for pile_class, info in distributions.items():
                    results_str += f"📋 {pile_class}:\n"
                    if info.get('sample_count', 0) == 0:
                        results_str += f"   未能加载该类别的数据或数据无效。\n\n"
                        continue

                    results_str += f"   样本文件数: {info.get('sample_count', 'N/A')}\n"
                    results_str += f"   数据点数: {info.get('total_points', 'N/A')}\n"
                    
                    fm = info.get('feature_means', {})
                    fs = info.get('feature_stds', {})

                    results_str += f"   平均波速 (均值): {fm.get('Speed_Mean', 'N/A'):.2f}%\n" if isinstance(fm.get('Speed_Mean'), (int, float)) else "   平均波速 (均值): N/A\n"
                    results_str += f"   平均波幅 (均值): {fm.get('Amp_Mean', 'N/A'):.2f}dB\n\n" if isinstance(fm.get('Amp_Mean'), (int, float)) else "   平均波幅 (均值): N/A\n\n"
            else:
                results_str = f"❌ 学习失败: {status}"
        else:
            results_str = "尚未开始学习或学习结果不可用。"
        
        self.results_text.insert(tk.END, results_str)

    def update_visualization(self):
        """更新可视化图表"""
        self.clear_plots() # 先清空

        if not self.learning_results or \
           self.learning_results.get('learning_status') != 'success' or \
           not self.learning_results.get('class_distributions'):
            self.canvas.draw_idle()
            return

        distributions = self.learning_results['class_distributions']
        pile_classes_learned = [pc for pc, info in distributions.items() if info.get('sample_count', 0) > 0]
        
        if not pile_classes_learned: # 如果没有一个类别成功学习
            self.canvas.draw_idle()
            return

        colors = ['#3498db', '#2ecc71', '#f1c40f', '#e74c3c', '#9b59b6'] # 增加颜色以防类别多

        # 图1: 样本文件数量
        sample_counts = [distributions[pc]['sample_count'] for pc in pile_classes_learned]
        self.axes[0, 0].bar(pile_classes_learned, sample_counts, color=[colors[i % len(colors)] for i in range(len(pile_classes_learned))])
        self.axes[0, 0].set_ylabel('样本文件数量', fontsize=9)
        self.axes[0, 0].set_title('各类桩样本文件数量', fontsize=10, fontweight='bold')


        # 图2: 数据点数量
        point_counts = [distributions[pc]['total_points'] for pc in pile_classes_learned]
        self.axes[0, 1].bar(pile_classes_learned, point_counts, color=[colors[i % len(colors)] for i in range(len(pile_classes_learned))])
        self.axes[0, 1].set_ylabel('数据点数量', fontsize=9)
        self.axes[0, 1].set_title('各类桩总数据点数量', fontsize=10, fontweight='bold')

        # 图3: 平均波速
        speed_means_valid = []
        pile_classes_for_speed = []
        for pc in pile_classes_learned:
            mean_val = distributions[pc]['feature_means'].get('Speed_Mean')
            if isinstance(mean_val, (int, float)):
                speed_means_valid.append(mean_val)
                pile_classes_for_speed.append(pc)
        if speed_means_valid:
            self.axes[1, 0].bar(pile_classes_for_speed, speed_means_valid, color=[colors[i % len(colors)] for i in range(len(pile_classes_for_speed))])
        self.axes[1, 0].set_ylabel('平均波速 (%)', fontsize=9)
        self.axes[1, 0].set_title('各类桩平均波速(%)', fontsize=10, fontweight='bold')


        # 图4: 平均波幅
        amp_means_valid = []
        pile_classes_for_amp = []
        for pc in pile_classes_learned:
            mean_val = distributions[pc]['feature_means'].get('Amp_Mean')
            if isinstance(mean_val, (int, float)):
                amp_means_valid.append(mean_val)
                pile_classes_for_amp.append(pc)
        if amp_means_valid:
            self.axes[1, 1].bar(pile_classes_for_amp, amp_means_valid, color=[colors[i % len(colors)] for i in range(len(pile_classes_for_amp))])
        self.axes[1, 1].set_ylabel('平均波幅 (dB)', fontsize=9)
        self.axes[1, 1].set_title('各类桩平均波幅(dB)', fontsize=10, fontweight='bold')


        for ax in self.axes.flat:
            ax.grid(True, linestyle='--', alpha=0.6)
            ax.tick_params(axis='x', rotation=30, labelsize=8) # 旋转标签避免重叠
            ax.tick_params(axis='y', labelsize=8)
        
        self.fig.tight_layout(pad=2.0)
        self.canvas.draw_idle()

    def _prepare_generation(self):
        """准备生成操作，检查学习状态和用户输入"""
        if not self.learning_results or self.learning_results.get('learning_status') != 'success' or self.learning_results.get('total_samples',0) == 0:
            messagebox.showerror("错误", "请先成功学习数据分布，并确保至少有一个类别的数据被学习。")
            return False
        
        self.generator.apply_augmentation = self.apply_augmentation_var.get() # 确保使用最新的GUI设置

        try:
            sample_count = int(self.sample_count_var.get())
            if sample_count <= 0:
                messagebox.showerror("错误", "样本数量必须大于0。")
                return False
            return sample_count
        except ValueError:
            messagebox.showerror("错误", "请输入有效的样本数量 (整数)。")
            return False

    def start_generation(self):
        """开始生成单个类别数据"""
        sample_count = self._prepare_generation()
        if not sample_count:
            return

        pile_class_to_generate = self.pile_class_var.get()
        if pile_class_to_generate not in self.generator.learned_distributions or \
           self.generator.learned_distributions[pile_class_to_generate] is None:
            messagebox.showerror("错误", f"无法为 {pile_class_to_generate} 生成数据：该类别的分布未学习或学习失败。")
            return


        self.generate_btn.config(state='disabled')
        self.generate_all_btn.config(state='disabled')
        self.learn_btn.config(state='disabled')
        self.browse_btn.config(state='disabled')

        self.progress_var.set(0) # 重置进度条
        self.progress_text.config(text=f"开始生成 {pile_class_to_generate} 数据...", style='Info.TLabel')

        thread = threading.Thread(target=self.generation_worker,
                                 args=(pile_class_to_generate, sample_count))
        thread.daemon = True
        thread.start()

    def start_generation_all(self):
        """开始生成所有类别数据"""
        sample_count = self._prepare_generation()
        if not sample_count:
            return

        self.generate_btn.config(state='disabled')
        self.generate_all_btn.config(state='disabled')
        self.learn_btn.config(state='disabled')
        self.browse_btn.config(state='disabled')

        self.progress_var.set(0) # 重置进度条
        self.progress_text.config(text="开始生成所有学习到的类别数据...", style='Info.TLabel')

        thread = threading.Thread(target=self.generation_all_worker, args=(sample_count,))
        thread.daemon = True
        thread.start()

    def generation_worker(self, pile_class: str, sample_count: int):
        """单个类别生成工作线程"""
        try:
            generated_data = self.generator.generate_intelligent_data(
                pile_class, sample_count, self.update_progress)
            
            if generated_data.empty:
                 self.root.after(0, lambda: self.generation_error(f"{pile_class} 未能生成有效数据。"))
                 return

            self.save_generated_data(pile_class, generated_data, sample_count)
            self.root.after(0, lambda: self.generation_completed_single(pile_class, sample_count, len(generated_data)))
        except Exception as e:
            self.root.after(0, lambda: self.generation_error(str(e)))
        finally: # 确保按钮总是恢复
            self.root.after(100, self._restore_buttons_after_task)


    def generation_all_worker(self, sample_count_per_class: int):
        """所有类别生成工作线程"""
        total_generated_count = 0
        successfully_generated_classes = []
        
        # 只为成功学习到分布的类别生成数据
        pile_classes_to_generate = [
            pc for pc, dist in self.generator.learned_distributions.items() if dist is not None
        ]
        
        if not pile_classes_to_generate:
            self.root.after(0, lambda: self.generation_error("没有可供生成数据的已学习类别。"))
            return

        num_target_classes = len(pile_classes_to_generate)
        
        try:
            for i, pile_class in enumerate(pile_classes_to_generate):
                current_progress_base = (i / num_target_classes) * 100
                
                # 更新主进度信息
                self.update_progress(current_progress_base, f"正在处理 {pile_class} ({i+1}/{num_target_classes})...")

                try:
                    # 定义一个局部的progress_callback，将子任务进度映射到整体进度
                    def sub_progress_callback(sub_p, sub_m):
                        overall_p = current_progress_base + (sub_p / num_target_classes)
                        self.update_progress(overall_p, f"{sub_m} (类别 {i+1}/{num_target_classes})")

                    generated_data = self.generator.generate_intelligent_data(
                        pile_class, sample_count_per_class, sub_progress_callback)
                    
                    if not generated_data.empty:
                        self.save_generated_data(pile_class, generated_data, sample_count_per_class)
                        total_generated_count += len(generated_data)
                        successfully_generated_classes.append(pile_class)
                    else:
                        print(f"警告: {pile_class} 未能生成有效数据，已跳过。")
                        # 即使一个类别失败，也应更新整体进度到该类别结束
                        self.update_progress(current_progress_base + (100 / num_target_classes), f"{pile_class} 生成失败或无数据。")


                except ValueError as ve: #捕获generate_intelligent_data可能抛出的ValueError
                    print(f"为 {pile_class} 生成数据时出错: {ve}")
                    self.update_progress(current_progress_base + (100 / num_target_classes), f"{pile_class} 生成失败: {ve}")


            self.root.after(0, lambda: self.generation_completed_all(successfully_generated_classes, sample_count_per_class, total_generated_count))
        except Exception as e:
            self.root.after(0, lambda: self.generation_error(str(e)))
        finally: # 确保按钮总是恢复
             self.root.after(100, self._restore_buttons_after_task)


    def _restore_buttons_after_task(self):
        """恢复按钮状态"""
        self.generate_btn.config(state='normal' if self.learning_results.get('total_samples',0)>0 else 'disabled')
        self.generate_all_btn.config(state='normal' if self.learning_results.get('total_samples',0)>0 else 'disabled')
        self.learn_btn.config(state='normal')
        self.browse_btn.config(state='normal')


    def save_generated_data(self, pile_class: str, data: pd.DataFrame, num_target_samples: int):
        """保存生成的数据"""
        if data.empty:
            print(f"没有为 {pile_class} 生成数据，不进行保存。")
            return

        class_short = pile_class.replace('类桩', '')
        save_dir_base = self.data_directory # 保存到选择的根目录
        
        # 创建一个名为 "Generated_Data" 的子目录
        generated_data_root_dir = os.path.join(save_dir_base, "Generated_Data_Output")
        os.makedirs(generated_data_root_dir, exist_ok=True)

        # 在 "Generated_Data" 下为每个桩类别创建子目录
        save_dir_class = os.path.join(generated_data_root_dir, class_short)
        os.makedirs(save_dir_class, exist_ok=True)


        # 按样本分组保存，每个样本一个文件
        # 假设每个样本的深度点数是可变的，需要找到样本的分割点
        # 这里简化处理：如果 num_target_samples > 0, 尝试平均分割
        # 一个更好的方法是，如果知道每个样本的原始点数，或者在生成时就按样本生成
        
        # 当前的 _generate_depth_sequence 是为单个样本生成一系列深度点
        # generate_intelligent_data 调用它 num_samples 次，所以 data 已经是 num_target_samples 个样本的集合了
        # 我们需要将 data 分割成 num_target_samples 个文件

        points_per_sample = len(data) // num_target_samples if num_target_samples > 0 else len(data)
        if points_per_sample == 0 and len(data) > 0: # 如果数据点数少于目标样本数，每个样本至少1个点
            points_per_sample = 1

        for i in range(num_target_samples):
            start_idx = i * points_per_sample
            end_idx = (i + 1) * points_per_sample
            
            if start_idx >= len(data): # 如果数据点不够了
                break
            if end_idx > len(data):
                end_idx = len(data)

            sample_data = data.iloc[start_idx:end_idx]
            if sample_data.empty:
                continue

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") # %f for microseconds
            filename = f"synthetic_{class_short}_{timestamp}_{i+1:03d}.txt"
            filepath = os.path.join(save_dir_class, filename)

            output_data = sample_data[['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']].copy()
            # 标准化输出列名
            output_data.columns = ['Depth(m)', 'S1(%)', 'A1(dB)', 'S2(%)', 'A2(dB)', 'S3(%)', 'A3(dB)']
            output_data.to_csv(filepath, sep='\t', index=False, float_format='%.2f')
        
        print(f"{pile_class} 数据已保存到: {save_dir_class}")


    def generation_completed_single(self, pile_class: str, num_samples: int, total_points: int):
        """单个类别生成完成"""
        self._restore_buttons_after_task()
        self.progress_var.set(100)
        msg = f"✅ {pile_class} 数据生成完成！\n共 {num_samples} 个样本, {total_points} 个数据点。"
        self.progress_text.config(text=msg, style='Success.TLabel')
        messagebox.showinfo("生成完成", f"{msg}\n数据已保存到原始目录下的 'Generated_Data_Output' 子目录中。")

    def generation_completed_all(self, generated_classes: List[str], count_per_class: int, total_points: int):
        """所有类别生成完成"""
        self._restore_buttons_after_task()
        self.progress_var.set(100)
        
        if not generated_classes:
            msg = "❌ 未能成功生成任何类别的数据。"
            self.progress_text.config(text=msg, style='Error.TLabel')
            messagebox.showwarning("生成结果", msg)
            return

        class_str = ", ".join(generated_classes)
        msg = f"✅ 所有选定类别数据生成完成！\n类别: {class_str}\n每类目标 {count_per_class} 个样本, 总计 {total_points} 个数据点。"
        self.progress_text.config(text=msg, style='Success.TLabel')
        messagebox.showinfo("全部生成完成", f"{msg}\n数据已保存到原始目录下的 'Generated_Data_Output' 子目录中。")


    def generation_error(self, error_message: str):
        """生成出错"""
        self._restore_buttons_after_task()
        self.progress_text.config(text=f"生成失败: {error_message}", style='Error.TLabel')
        messagebox.showerror("生成失败", f"数据生成过程中出现错误:\n{error_message}")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = IntelligentDataGeneratorGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
