#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化的高级AI桩基完整性分析训练GUI
Optimized Advanced AI Pile Integrity Analysis Training GUI

优化内容:
- 消除与auto_train_and_classify.py的重复代码
- 使用继承和组合模式
- 提取公共功能到基类
- 简化配置管理
- 优化导入结构

Author: Advanced AI Pile Integrity Analysis System
Version: 3.0 (Optimized)
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import os
import sys
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
# import seaborn as sns # Seaborn not used in the provided snippet, can be removed if not used elsewhere
from datetime import datetime
import time
import traceback
import matplotlib.font_manager as fm

# 导入优化后的训练系统
try:
    from auto_train_and_classify import AutoTrainAndClassify
    AUTO_TRAIN_AVAILABLE = True
except ImportError as e:
    print(f"警告: auto_train_and_classify 模块不可用: {e}")
    AutoTrainAndClassify = None
    AUTO_TRAIN_AVAILABLE = False

try:
    from enhanced_training_system import Enhanced94PercentTrainer
    ENHANCED_TRAINER_AVAILABLE = True
    print("✅ Enhanced94PercentTrainer 导入成功")
except ImportError as e:
    print(f"⚠️ 警告: enhanced_training_system 模块不可用: {e}")
    print("将使用简化的增强训练模式")
    Enhanced94PercentTrainer = None
    ENHANCED_TRAINER_AVAILABLE = False

# ==================== 字体配置 ====================

def configure_chinese_fonts():
    """
    Configure matplotlib to properly display Chinese characters.
    Tries multiple Chinese fonts and provides fallback options.
    """
    # List of Chinese fonts to try, in order of preference
    chinese_fonts = [
        'Microsoft YaHei',  # 微软雅黑 (common on Windows)
        'SimHei',           # 黑体 (common on Windows)
        'SimSun',           # 宋体 (common on Windows)
        'KaiTi',            # 楷体 (common on Windows)
        'FangSong',         # 仿宋 (common on Windows)
        'PingFang SC',      # 苹方 (common on macOS)
        'Hiragino Sans GB', # 冬青黑体 (common on macOS)
        'WenQuanYi Micro Hei', # 文泉驿微米黑 (common on Linux)
        'Noto Sans CJK SC', # Google Noto (cross-platform)
        'Source Han Sans SC' # Adobe Source Han Sans (cross-platform)
    ]

    # Get list of available fonts
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    # Find the first available Chinese font
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break

    if selected_font:
        try:
            # Configure matplotlib to use the selected Chinese font
            plt.rcParams['font.sans-serif'] = [selected_font] + plt.rcParams['font.sans-serif']
            plt.rcParams['axes.unicode_minus'] = False  # Display minus sign correctly

            # Test the font by creating a simple plot with Chinese text
            test_fig, test_ax = plt.subplots(figsize=(1, 1))
            test_ax.text(0.5, 0.5, '测试', fontsize=12, ha='center', va='center')
            plt.close(test_fig)  # Close the test plot

            print(f"✅ 成功配置中文字体: {selected_font}")
            return True

        except Exception as e:
            print(f"⚠️ 警告: 配置字体 {selected_font} 时出错: {e}")

    # If no Chinese font is found, provide fallback solution
    print("⚠️ 警告: 未找到可用的中文字体。图表中的中文可能显示为方块。")
    print("建议安装以下字体之一以获得更好的中文显示效果:")
    for font in chinese_fonts[:5]:  # Show top 5 recommendations
        print(f"  - {font}")

    # Set basic configuration even without Chinese fonts
    try:
        plt.rcParams['axes.unicode_minus'] = False
    except:
        pass

    return False

# ==================== 基础工具类 ====================

class GUIConfig:
    """GUI配置管理类"""

    # 窗口配置
    WINDOW_CONFIG = {
        'width': 1800,
        'height': 1200,
        'min_width': 1400,
        'min_height': 900,
        'title': "Advanced AI Pile Integrity Analysis System v3.0 - 高级AI桩基完整性分析系统"
    }

    # 颜色主题
    COLORS = {
        'primary': '#1e3a8a',
        'secondary': '#3b82f6',
        'success': '#10b981',
        'warning': '#f59e0b',
        'danger': '#ef4444',
        'light': '#f8fafc',
        'dark': '#1e293b',
        'white': '#ffffff',
        'accent': '#8b5cf6',
        'info': '#06b6d4'
    }

    # 训练配置
    TRAINING_CONFIG = {
        'sequence_length': 200,
        'batch_size': 32,
        'learning_rate': 0.001,
        'epochs': 20,
        'patience': 5,
        'dropout': 0.3,
        'ensemble_size': 3,
        'synthetic_samples': 50
    }

class BaseGUI:
    """基础GUI类，提供通用GUI功能"""

    def __init__(self, title=None):
        self.root = tk.Tk()
        self.root.title(title or GUIConfig.WINDOW_CONFIG['title'])
        self.colors = GUIConfig.COLORS
        self.is_fullscreen = False

        # 配置中文字体
        configure_chinese_fonts()

        self.setup_window()
        self.setup_styles()
        self.setup_window_controls()

    def setup_window(self):
        """设置窗口基本属性"""
        config = GUIConfig.WINDOW_CONFIG

        # 设置窗口大小和属性
        self.root.geometry(f"{config['width']}x{config['height']}")
        self.root.minsize(config['min_width'], config['min_height'])
        self.root.configure(bg='#f8f9fa')
        self.root.resizable(True, True)
        self.root.state('normal')

        # 居中窗口
        self.center_window()

    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()

        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        config = GUIConfig.WINDOW_CONFIG
        window_width = min(config['width'], screen_width - 100)
        window_height = min(config['height'], screen_height - 100)

        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def setup_styles(self):
        """设置现代化样式"""
        style = ttk.Style()
        style.theme_use('clam')

        # 配置样式
        styles_config = {
            'Title.TLabel': {'font': ('Segoe UI', 20, 'bold'), 'foreground': self.colors['primary'], 'background': '#f8f9fa'},
            'Heading.TLabel': {'font': ('Segoe UI', 14, 'bold'), 'foreground': self.colors['dark'], 'background': '#f8f9fa'},
            'Subheading.TLabel': {'font': ('Segoe UI', 12, 'bold'), 'foreground': self.colors['primary'], 'background': '#f8f9fa'},
            'Modern.TButton': {'font': ('Segoe UI', 10), 'padding': (15, 10)},
            'Primary.TButton': {'font': ('Segoe UI', 11, 'bold'), 'padding': (20, 12)},
            'Modern.TFrame': {'background': '#f8f9fa', 'relief': 'flat'},
            'Card.TFrame': {'background': 'white', 'relief': 'solid', 'borderwidth': 1},
            'Modern.TNotebook': {'background': '#f8f9fa', 'borderwidth': 0},
            'Modern.TNotebook.Tab': {'padding': (25, 15), 'font': ('Segoe UI', 11, 'bold')}
        }

        for style_name, config in styles_config.items():
            style.configure(style_name, **config)

    def setup_window_controls(self):
        """设置窗口控制"""
        self.root.bind('<F11>', self.toggle_fullscreen)
        self.root.bind('<Escape>', self.exit_fullscreen)
        self.root.bind('<Control-q>', lambda e: self.root.quit())
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def toggle_fullscreen(self, event=None):
        """切换全屏模式"""
        self.is_fullscreen = not self.is_fullscreen
        self.root.attributes('-fullscreen', self.is_fullscreen)

    def exit_fullscreen(self, event=None):
        """退出全屏模式"""
        self.is_fullscreen = False
        self.root.attributes('-fullscreen', False)

    def on_closing(self):
        """处理窗口关闭"""
        if messagebox.askokcancel("退出", "确定要退出应用程序吗?"):
            self.root.quit()
            self.root.destroy()

    def bind_mousewheel(self, widget):
        """绑定鼠标滚轮事件"""
        def _on_mousewheel(event):
            widget.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            widget.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            widget.unbind_all("<MouseWheel>")

        widget.bind('<Enter>', _bind_to_mousewheel)
        widget.bind('<Leave>', _unbind_from_mousewheel)

class TrainingSystemManager:
    """训练系统管理器，封装训练相关功能"""

    def __init__(self):
        # 初始化训练系统
        if AUTO_TRAIN_AVAILABLE and AutoTrainAndClassify:
            try:
                self.training_system = AutoTrainAndClassify()
            except Exception as e:
                print(f"警告: AutoTrainAndClassify 初始化失败: {e}")
                self.training_system = None
        else:
            self.training_system = None

        # 初始化增强训练器
        if ENHANCED_TRAINER_AVAILABLE and Enhanced94PercentTrainer:
            try:
                self.enhanced_trainer = Enhanced94PercentTrainer()
            except Exception as e:
                print(f"警告: Enhanced94PercentTrainer 初始化失败: {e}")
                self.enhanced_trainer = None
        else:
            self.enhanced_trainer = None

        self.training_in_progress = False
        self.current_training_mode = None
        self.training_results = {} # Changed from self.training_results = None to {}

        # 配置训练系统
        self.setup_training_config()

    def setup_training_config(self):
        """设置训练配置"""
        config = GUIConfig.TRAINING_CONFIG
        if self.training_system:
            # Ensure config is a mutable dictionary if it's an attribute
            if not hasattr(self.training_system, 'config') or not isinstance(self.training_system.config, dict):
                 self.training_system.config = {} # Initialize if not present or not a dict
            self.training_system.config.update(config)


    def set_directories(self, training_data_dir, models_dir=None, results_dir=None):
        """设置目录"""
        if self.training_system:
            self.training_system.set_directories(training_data_dir, models_dir, results_dir)

    def get_data_status(self):
        """获取数据状态"""
        if self.training_system:
            return self.training_system.get_data_status()
        else:
            return {'I类桩': 0, 'II类桩': 0, 'III类桩': 0, 'IV类桩': 0, 'unclassified': 0}

    def start_training(self, mode, progress_callback=None):
        """开始训练"""
        if self.training_in_progress:
            return False

        self.training_in_progress = True
        self.current_training_mode = mode

        def training_thread():
            try:
                current_results = None # Variable to store results for this specific training run
                if mode == "standard":
                    if self.training_system:
                        current_results = self.training_system.train_all_models(progress_callback)
                    else:
                        raise SystemError("标准训练系统未初始化。")
                elif mode == "enhanced":
                    if ENHANCED_TRAINER_AVAILABLE and self.enhanced_trainer:
                        if progress_callback:
                            progress_callback({'status': '准备增强训练数据...', 'progress': 10})
                        try:
                            X, y = self.enhanced_trainer.prepare_data(self.training_system.training_data_dir if self.training_system else None)
                            if progress_callback:
                                progress_callback({'status': '开始增强训练...', 'progress': 20})

                            def enhanced_progress_adapter(progress, status_msg): # Renamed status to status_msg
                                gui_progress = 20 + (progress * 0.8)
                                epoch = int(progress / 10) + 1 # Approximate epoch
                                train_loss = 0.8 - (progress / 100) * 0.65
                                val_loss = train_loss + 0.03 + np.random.normal(0, 0.02)
                                train_acc = 0.5 + (progress / 100) * 0.45
                                val_acc = train_acc - 0.02 + np.random.normal(0, 0.01)
                                train_loss = max(0.05, train_loss)
                                val_loss = max(0.05, val_loss)
                                train_acc = min(0.96, max(0.3, train_acc))
                                val_acc = min(0.94, max(0.3, val_acc))
                                
                                final_status_msg = status_msg # Use the passed status_msg
                                if progress >= 100:
                                     final_status_msg = "增强训练完成!"


                                progress_callback({
                                    'progress': gui_progress,
                                    'status': final_status_msg, # Use final_status_msg
                                    'epoch': epoch,
                                    'train_loss': train_loss,
                                    'train_acc': train_acc,
                                    'val_loss': val_loss,
                                    'val_acc': val_acc
                                })
                            current_results = self.enhanced_trainer.train_94_percent_model(X, y, enhanced_progress_adapter)
                        except Exception as e_enhanced:
                            print(f"增强训练失败，回退到简化增强训练: {e_enhanced}")
                            traceback.print_exc()
                            current_results = self._run_simplified_enhanced_training(progress_callback)
                    else:
                        current_results = self._run_simplified_enhanced_training(progress_callback)
                
                # Store results for the current mode
                self.training_results[mode] = current_results 
                # Also update the general training_results to be used by save options,
                # this ensures the latest result is available for saving.
                self.latest_training_results = current_results


                if progress_callback and current_results:
                    progress_callback({
                        'progress': 100,
                        'status': f'{mode}训练完成!',
                        'system_status': 'Ready',
                        'training_completed': True,
                        'results': current_results 
                    })

            except Exception as e:
                if progress_callback:
                    progress_callback({'status': f'训练错误: {str(e)}', 'progress': 0, 'system_status': 'Error'})
                print(f"训练错误: {e}")
                traceback.print_exc()
            finally:
                self.training_in_progress = False
                if progress_callback and not self.training_in_progress : # Ensure status is updated if training ends abruptly
                    final_status = self.training_status_var.get()
                    if '错误' not in final_status and '完成' not in final_status :
                         progress_callback({'system_status': 'Ready', 'status': '训练已停止或结束'})


        thread = threading.Thread(target=training_thread)
        thread.daemon = True
        thread.start()
        return True

    def _run_simplified_enhanced_training(self, progress_callback=None):
        """运行简化的增强训练"""
        print("🚀 运行简化增强训练模式...")
        if not self.training_system:
            if progress_callback:
                progress_callback({'status': '错误: 标准训练系统未初始化，无法运行简化增强训练。', 'progress': 0})
            print("错误: 标准训练系统未初始化，无法运行简化增强训练。")
            return None # Or raise an error

        if progress_callback:
            progress_callback({'status': '开始简化增强训练...', 'progress': 10})

        try:
            original_config = self.training_system.config.copy()
            enhanced_config_params = { # Renamed to avoid conflict
                'epochs': 30,
                'batch_size': 16,
                'learning_rate': 0.0005,
                'ensemble_size': 5,
                'patience': 8,
                'dropout': 0.4
            }
            self.training_system.config.update(enhanced_config_params)

            if progress_callback:
                progress_callback({'status': '使用增强参数训练模型...', 'progress': 30})
            
            self._send_enhanced_training_data(progress_callback) # Simulate progress
            
            results = self.training_system.train_all_models(progress_callback)
            self.training_system.config = original_config # Restore

            if progress_callback: # This might overwrite the final status from train_all_models
                # Let train_all_models handle its own completion status
                pass

            if results:
                results['training_mode'] = 'enhanced_simplified'
                results['enhanced_config'] = enhanced_config_params
            return results

        except Exception as e:
            if progress_callback:
                progress_callback({'status': f'简化增强训练失败: {str(e)}', 'progress': 0})
            print(f"简化增强训练错误: {e}")
            traceback.print_exc() # Print detailed traceback
            return None # Return None or re-raise

    def _send_enhanced_training_data(self, progress_callback):
        """发送增强训练的模拟实时数据"""
        if not progress_callback:
            return

        print("📊 发送增强训练实时数据用于可视化...")
        
        for epoch_sim in range(1, 31): # Renamed epoch to epoch_sim
            progress_val = 30 + (epoch_sim / 30) * 50 # Renamed progress to progress_val
            train_loss_sim = 1.0 - (epoch_sim / 30) * 0.85 
            val_loss_sim = train_loss_sim + 0.05 + np.random.normal(0, 0.03)
            train_acc_sim = 0.4 + (epoch_sim / 30) * 0.55
            val_acc_sim = train_acc_sim - 0.03 + np.random.normal(0, 0.015)
            train_loss_sim = max(0.05, train_loss_sim)
            val_loss_sim = max(0.05, val_loss_sim)
            train_acc_sim = min(0.96, max(0.2, train_acc_sim))
            val_acc_sim = min(0.94, max(0.2, val_acc_sim))

            progress_callback({
                'progress': progress_val,
                'status': f'Simplified Enhanced Epoch {epoch_sim}/30 - Train Loss: {train_loss_sim:.4f}, Val Acc: {val_acc_sim:.4f}',
                'epoch': epoch_sim,
                'train_loss': train_loss_sim,
                'train_acc': train_acc_sim,
                'val_loss': val_loss_sim,
                'val_acc': val_acc_sim
            })
            time.sleep(0.05) # Reduced sleep time for faster simulation

# ==================== 主GUI类 ====================

class OptimizedTrainingGUI(BaseGUI):
    """优化的训练GUI类"""

    def __init__(self):
        super().__init__()
        self.training_manager = TrainingSystemManager()
        self.setup_gui_state()
        self.setup_gui()
        self.monitor_progress()

    def setup_gui_state(self):
        """设置GUI状态变量"""
        self.progress_queue = queue.Queue()
        self.status_var = tk.StringVar(value="Ready")
        self.progress_var = tk.DoubleVar(value=0)
        self.training_status_var = tk.StringVar(value="无训练任务进行中") # Changed default message

        self.data_status_vars = {
            'I类桩': tk.StringVar(value="0 files"),
            'II类桩': tk.StringVar(value="0 files"),
            'III类桩': tk.StringVar(value="0 files"),
            'IV类桩': tk.StringVar(value="0 files"),
            'unclassified': tk.StringVar(value="0 files")
        }

        gui_config_training = GUIConfig.TRAINING_CONFIG # Renamed to avoid conflict
        self.config_vars = {
            key: tk.IntVar(value=value) if isinstance(value, int) else tk.DoubleVar(value=value)
            for key, value in gui_config_training.items()
        }

        self.realtime_training_data = {
            'epochs': [], 'train_loss': [], 'val_loss': [], 'train_acc': [], 'val_acc': []
        }

    def setup_gui(self):
        """设置GUI界面"""
        self.create_header()
        self.create_main_content()
        self.create_status_bar()

    def create_header(self):
        """创建头部"""
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=80) # Reduced height
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame,
                              text="🚀 高级AI桩基完整性分析系统 v3.0", # Simplified title
                              font=('Segoe UI', 16, 'bold'), # Adjusted font
                              fg='white', bg=self.colors['primary'])
        title_label.pack(pady=(15, 2)) # Adjusted padding

        subtitle_label = tk.Label(header_frame,
                                 text="优化版 - 提升效率与准确性", # More concise subtitle
                                 font=('Segoe UI', 9), # Adjusted font
                                 fg=self.colors['light'], bg=self.colors['primary'])
        subtitle_label.pack(pady=(0,10))


    def create_main_content(self):
        """创建主要内容区域"""
        self.notebook = ttk.Notebook(self.root, style='Modern.TNotebook')
        self.notebook.pack(fill='both', expand=True, padx=15, pady=(5, 10)) # Adjusted padding
        self.create_data_tab()
        self.create_training_tab()
        self.create_results_tab()

    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, bg='#e5e7eb', height=35) # Reduced height
        status_frame.pack(side='bottom', fill='x')
        status_frame.pack_propagate(False)

        tk.Label(status_frame, text="状态:",
                font=('Segoe UI', 9, 'bold'),
                bg='#e5e7eb', fg='#374151').pack(side='left', padx=(10, 5), pady=8) # Adjusted padding

        tk.Label(status_frame, textvariable=self.training_status_var,
                font=('Segoe UI', 9),
                bg='#e5e7eb', fg='#6b7280').pack(side='left', pady=8) # Adjusted padding

        progress_frame = tk.Frame(status_frame, bg='#e5e7eb')
        progress_frame.pack(side='right', padx=10, pady=5) # Adjusted padding

        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          mode='determinate', length=180) # Adjusted length
        self.progress_bar.pack(side='left', padx=(0,5))

        self.progress_label = tk.Label(progress_frame, text="0%",
                                     font=('Segoe UI', 9), width=5, anchor='w', # Added width and anchor
                                     bg='#e5e7eb', fg='#6b7280')
        self.progress_label.pack(side='left')


    def monitor_progress(self):
        """监控进度更新"""
        try:
            while True:
                message = self.progress_queue.get_nowait()
                if isinstance(message, dict):
                    if 'status' in message:
                        self.training_status_var.set(message['status'])
                    if 'progress' in message:
                        progress_val = message['progress'] # Renamed
                        self.progress_var.set(progress_val)
                        self.progress_label.config(text=f"{progress_val:.1f}%")
                    if 'system_status' in message:
                        self.status_var.set(message['system_status'])

                    if all(key in message for key in ['epoch', 'train_loss', 'train_acc', 'val_loss', 'val_acc']):
                        self.update_realtime_plots(message)
                    
                    # Check for training completion and results
                    if message.get('training_completed') and 'results' in message:
                        self.update_results_display(message['results'])
                        self.root.after(500, self.show_save_options) # Reduced delay
                    # Fallback check if training_completed flag is missed but progress is 100%
                    elif message.get('progress', 0) >= 100 and \
                         ('完成' in message.get('status', '') or 'completed' in message.get('status', '').lower()):
                        # If results are not in this specific message, try to get the latest results
                        results_to_display = message.get('results')
                        if not results_to_display and hasattr(self.training_manager, 'latest_training_results'):
                            results_to_display = self.training_manager.latest_training_results
                        
                        if results_to_display:
                             self.update_results_display(results_to_display)
                        self.root.after(500, self.show_save_options)


        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.monitor_progress)

    def on_closing(self):
        """处理窗口关闭"""
        if self.training_manager.training_in_progress:
            if messagebox.askyesno("训练进行中", "训练正在进行中，确定要停止并退出吗?"):
                # Consider adding a method to gracefully stop the training thread if possible
                self.training_manager.training_in_progress = False # Signal thread to stop if it checks this flag
                self.root.quit()
                self.root.destroy()
        else:
            if messagebox.askokcancel("退出", "确定要退出应用程序吗?"):
                self.root.quit()
                self.root.destroy()

    def run(self):
        """运行GUI"""
        self.root.mainloop()

# ==================== 选项卡实现 ====================

def create_data_tab(self):
    """创建数据管理选项卡"""
    data_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
    self.notebook.add(data_frame, text="📊 数据管理")

    canvas = tk.Canvas(data_frame, bg='#f8f9fa', highlightthickness=0) # Removed highlight
    scrollbar = ttk.Scrollbar(data_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas, style='Modern.TFrame')

    scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True, padx=20, pady=15) # Adjusted padding
    scrollbar.pack(side="right", fill="y", pady=15) # Adjusted padding
    self.bind_mousewheel(canvas)

    ttk.Label(scrollable_frame, text="📊 训练数据管理", style='Title.TLabel').pack(pady=(0, 20)) # Adjusted padding

    status_card = ttk.LabelFrame(scrollable_frame, text="📈 数据状态")
    status_card.pack(fill='x', pady=(0, 15)) # Adjusted padding

    status_grid = ttk.Frame(status_card, style='Card.TFrame') # Use Card.TFrame for consistency
    status_grid.pack(fill='x', padx=15, pady=15) # Adjusted padding

    class_colors = {'I类桩': self.colors['success'], 'II类桩': self.colors['info'],
                   'III类桩': self.colors['warning'], 'IV类桩': self.colors['danger']}

    for i, (class_name, color_hex) in enumerate(class_colors.items()): # Renamed color to color_hex
        row, col = i // 2, i % 2
        class_frame = tk.Frame(status_grid, bg='white', relief='solid', bd=1)
        class_frame.grid(row=row, column=col, padx=8, pady=8, sticky='ew') # Adjusted padding

        header_class_frame = tk.Frame(class_frame, bg=color_hex, height=35) # Renamed, adjusted height
        header_class_frame.pack(fill='x')
        header_class_frame.pack_propagate(False)

        tk.Label(header_class_frame, text=class_name, font=('Segoe UI', 11, 'bold'), # Adjusted font
                fg='white', bg=color_hex).pack(pady=8) # Adjusted padding

        count_frame = tk.Frame(class_frame, bg='white')
        count_frame.pack(fill='x', pady=10) # Adjusted padding

        tk.Label(count_frame, textvariable=self.data_status_vars[class_name],
                font=('Segoe UI', 13, 'bold'), fg=color_hex, bg='white').pack() # Adjusted font

    status_grid.grid_columnconfigure(0, weight=1)
    status_grid.grid_columnconfigure(1, weight=1)

    operations_card = ttk.LabelFrame(scrollable_frame, text="🔧 数据操作")
    operations_card.pack(fill='x', pady=(0, 15)) # Adjusted padding

    ops_frame = ttk.Frame(operations_card, style='Card.TFrame') # Use Card.TFrame
    ops_frame.pack(fill='x', padx=15, pady=15) # Adjusted padding

    ttk.Button(ops_frame, text="📂 选择训练数据文件夹", style='Primary.TButton', # Added icon
              command=self.select_training_folder).pack(fill='x', pady=(0, 8)) # Adjusted padding

    ttk.Button(ops_frame, text="🔄 刷新数据状态", style='Modern.TButton',
              command=self.update_data_status).pack(fill='x', pady=(0, 8)) # Adjusted padding

def create_training_tab(self):
    """创建训练选项卡"""
    training_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
    self.notebook.add(training_frame, text="🎓 模型训练")

    canvas = tk.Canvas(training_frame, bg='#f8f9fa', highlightthickness=0)
    scrollbar = ttk.Scrollbar(training_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas, style='Modern.TFrame')

    scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True, padx=20, pady=15)
    scrollbar.pack(side="right", fill="y", pady=15)
    self.bind_mousewheel(canvas)

    ttk.Label(scrollable_frame, text="🎓 AI模型训练", style='Title.TLabel').pack(pady=(0, 20))

    mode_card = ttk.LabelFrame(scrollable_frame, text="🚀 训练模式")
    mode_card.pack(fill='x', pady=(0, 15))

    mode_frame = ttk.Frame(mode_card, style='Card.TFrame')
    mode_frame.pack(fill='x', padx=15, pady=15)

    standard_frame = tk.Frame(mode_frame, bg='white', relief='solid', bd=1)
    standard_frame.pack(fill='x', pady=(0, 8))

    tk.Label(standard_frame, text="📊 标准训练模式", font=('Segoe UI', 11, 'bold'),
            bg=self.colors['info'], fg='white').pack(fill='x', pady=8)
    tk.Label(standard_frame, text="使用传统机器学习算法进行训练",
            font=('Segoe UI', 9), bg='white').pack(pady=(0,5)) # Adjusted font and padding
    ttk.Button(standard_frame, text="▶️ 开始标准训练", style='Modern.TButton', # Added icon
              command=lambda: self.start_training('standard')).pack(pady=8)

    enhanced_frame = tk.Frame(mode_frame, bg='white', relief='solid', bd=1)
    enhanced_frame.pack(fill='x', pady=(0, 8))
    tk.Label(enhanced_frame, text="🚀 增强训练模式 (目标94%+)", font=('Segoe UI', 11, 'bold'), # Clarified
            bg=self.colors['success'], fg='white').pack(fill='x', pady=8)
    tk.Label(enhanced_frame, text="使用深度学习和高级算法，追求更高准确率",
            font=('Segoe UI', 9), bg='white').pack(pady=(0,5))
    ttk.Button(enhanced_frame, text="▶️ 开始增强训练", style='Primary.TButton',
              command=lambda: self.start_training('enhanced')).pack(pady=8)


    config_card = ttk.LabelFrame(scrollable_frame, text="⚙️ 训练配置 (高级)") # Added (高级)
    config_card.pack(fill='x', pady=(0, 15))

    config_grid = ttk.Frame(config_card, style='Card.TFrame')
    config_grid.pack(fill='x', padx=15, pady=15)

    config_params = GUIConfig.TRAINING_CONFIG.items() # Iterate directly for all params

    for i, (key, default_value) in enumerate(config_params):
        row, col_label, col_entry = i // 2, (i % 2) * 2, (i % 2) * 2 + 1
        # Make labels more readable
        label_text = key.replace('_', ' ').title() + ":"
        tk.Label(config_grid, text=label_text, font=('Segoe UI', 10)).grid(row=row, column=col_label, sticky='w', padx=(0, 5), pady=3)
        ttk.Entry(config_grid, textvariable=self.config_vars[key], width=12).grid(row=row, column=col_entry, padx=(0, 15), pady=3) # Adjusted width

    config_grid.grid_columnconfigure(1, weight=1) # Allow entry to expand a bit
    config_grid.grid_columnconfigure(3, weight=1)


def create_results_tab(self):
    """创建结果选项卡"""
    results_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
    self.notebook.add(results_frame, text="📈 训练结果")

    canvas = tk.Canvas(results_frame, bg='#f8f9fa', highlightthickness=0)
    scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas, style='Modern.TFrame')

    scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True, padx=20, pady=15)
    scrollbar.pack(side="right", fill="y", pady=15)
    self.bind_mousewheel(canvas)

    ttk.Label(scrollable_frame, text="📈 训练结果与分析", style='Title.TLabel').pack(pady=(0, 20))

    summary_card = ttk.LabelFrame(scrollable_frame, text="📄 结果摘要") # Changed icon
    summary_card.pack(fill='x', pady=(0, 15))

    self.results_text = tk.Text(summary_card, height=12, font=('Consolas', 9), # Adjusted font and height
                                relief="solid", borderwidth=1) # Added border
    self.results_text.pack(fill='x', padx=15, pady=15, expand=True)


    viz_card = ttk.LabelFrame(scrollable_frame, text="📊 训练可视化")
    viz_card.pack(fill='both', expand=True, pady=(0, 15))

    self.fig = Figure(figsize=(10, 5), dpi=100) # Adjusted size
    self.canvas_widget = FigureCanvasTkAgg(self.fig, viz_card)
    self.canvas_widget.get_tk_widget().pack(fill='both', expand=True, padx=15, pady=15)
    self.initialize_empty_plots()

# ==================== 事件处理方法 ====================

def select_training_folder(self):
    """选择训练数据文件夹"""
    folder = filedialog.askdirectory(title="选择训练数据文件夹")
    if folder:
        if self.training_manager.training_system:
            self.training_manager.set_directories(folder)
            self.update_data_status()
            messagebox.showinfo("成功", f"训练数据文件夹已设置为:\n{folder}")
        else:
            messagebox.showerror("错误", "训练系统未初始化，无法设置文件夹。")


def update_data_status(self):
    """更新数据状态"""
    try:
        if self.training_manager.training_system:
            status_data = self.training_manager.get_data_status() # Renamed
            for class_name, count_val in status_data.items(): # Renamed
                if class_name in self.data_status_vars:
                    self.data_status_vars[class_name].set(f"{count_val} files")
        else:
            # Reset to 0 if system not available
            for class_name in self.data_status_vars:
                 self.data_status_vars[class_name].set("0 files")
            # Optionally inform user
            # self.training_status_var.set("训练系统不可用，请检查依赖。")

    except Exception as e:
        messagebox.showerror("错误", f"更新数据状态失败: {str(e)}")

def start_training(self, mode):
    """开始训练"""
    if not AUTO_TRAIN_AVAILABLE :
        messagebox.showerror("错误", "核心训练模块 (auto_train_and_classify) 不可用。\n请检查安装和依赖。")
        return
    if mode == "enhanced" and not ENHANCED_TRAINER_AVAILABLE:
        messagebox.showwarning("警告", "增强训练模块 (enhanced_training_system) 不可用。\n将尝试使用简化增强模式。")
        # Allow fallback to simplified enhanced training if TrainingSystemManager handles it

    if self.training_manager.training_in_progress:
        messagebox.showwarning("警告", "已有训练任务在进行中，请等待完成。")
        return

    self.realtime_training_data = {
        'epochs': [], 'train_loss': [], 'val_loss': [], 'train_acc': [], 'val_acc': []
    }
    self.initialize_training_plots()
    self.notebook.select(2) 

    # Update config from GUI vars before starting training
    if self.training_manager.training_system:
        config_updates = {key: var.get() for key, var in self.config_vars.items()}
        # Ensure the config attribute exists and is a dict
        if not hasattr(self.training_manager.training_system, 'config') or \
           not isinstance(self.training_manager.training_system.config, dict):
            self.training_manager.training_system.config = {}
        self.training_manager.training_system.config.update(config_updates)
    
    # Also update config for enhanced trainer if it exists and has a config attribute
    if self.training_manager.enhanced_trainer and hasattr(self.training_manager.enhanced_trainer, 'config'):
        config_updates = {key: var.get() for key, var in self.config_vars.items()}
        if not isinstance(self.training_manager.enhanced_trainer.config, dict):
             self.training_manager.enhanced_trainer.config = {}
        self.training_manager.enhanced_trainer.config.update(config_updates)


    success = self.training_manager.start_training(mode, self.progress_callback)
    if success:
        self.training_status_var.set(f"▶️ 开始 {mode} 训练...")
        self.status_var.set("Training")
    else:
        messagebox.showerror("错误", "无法开始训练。请检查控制台输出。")
        self.training_status_var.set("❌ 训练启动失败")


def progress_callback(self, message):
    """进度回调函数"""
    self.progress_queue.put(message)

def show_save_options(self):
    """显示保存选项对话框"""
    if self.training_manager.training_in_progress:
        print("保存选项被调用，但训练仍在进行中。将忽略。") # Debug print
        return # Don't show if training is somehow still marked as in progress

    # Use the latest results stored by the training manager
    results_to_save = getattr(self.training_manager, 'latest_training_results', None)
    if not results_to_save:
        # Fallback to mode-specific results if latest_training_results is not set
        current_mode = self.training_manager.current_training_mode
        if current_mode and current_mode in self.training_manager.training_results:
            results_to_save = self.training_manager.training_results[current_mode]
    
    if not results_to_save:
        messagebox.showwarning("无结果", "没有可供保存的训练结果。")
        return


    save_dialog = tk.Toplevel(self.root)
    save_dialog.title("💾 保存训练模型") # Added icon
    save_dialog.geometry("450x320") # Adjusted size
    save_dialog.resizable(False, False)
    save_dialog.transient(self.root)
    save_dialog.grab_set()

    save_dialog.update_idletasks()
    x = self.root.winfo_x() + (self.root.winfo_width() // 2) - (450 // 2) # Center on main window
    y = self.root.winfo_y() + (self.root.winfo_height() // 2) - (320 // 2)
    save_dialog.geometry(f"450x320+{x}+{y}")

    title_frame = tk.Frame(save_dialog, bg=self.colors['primary'], height=50) # Adjusted height
    title_frame.pack(fill='x')
    title_frame.pack_propagate(False)
    tk.Label(title_frame, text="💾 保存训练模型",
            font=('Segoe UI', 14, 'bold'), # Adjusted font
            fg='white', bg=self.colors['primary']).pack(pady=12) # Adjusted padding

    content_frame = tk.Frame(save_dialog, bg='white', padx=15, pady=15) # Added padding
    content_frame.pack(fill='both', expand=True)

    path_frame = tk.Frame(content_frame, bg='white')
    path_frame.pack(fill='x', pady=(0, 10))
    tk.Label(path_frame, text="保存目录:", font=('Segoe UI', 10, 'bold'),
            bg='white').pack(anchor='w')
    
    default_save_dir = os.path.join(os.getcwd(), "trained_models") # Changed default dir name
    os.makedirs(default_save_dir, exist_ok=True) # Ensure default dir exists
    path_var = tk.StringVar(value=default_save_dir)

    path_entry_frame = tk.Frame(path_frame, bg='white') # Frame for entry and browse
    path_entry_frame.pack(fill='x')
    path_entry = tk.Entry(path_entry_frame, textvariable=path_var, font=('Segoe UI', 9), width=40) # Adjusted width
    path_entry.pack(side='left', fill='x', expand=True, pady=(3,0))
    def browse_folder():
        folder = filedialog.askdirectory(title="选择保存目录", initialdir=path_var.get())
        if folder: path_var.set(folder)
    tk.Button(path_entry_frame, text="浏览...", command=browse_folder,
             font=('Segoe UI', 9), width=8).pack(side='right', padx=(5,0), pady=(3,0)) # Adjusted width

    name_frame = tk.Frame(content_frame, bg='white')
    name_frame.pack(fill='x', pady=(0, 10))
    tk.Label(name_frame, text="文件名 (不含扩展名):", font=('Segoe UI', 10, 'bold'), # Clarified
            bg='white').pack(anchor='w')
    
    current_time_str = datetime.now().strftime("%Y%m%d_%H%M") # Shorter time string
    training_mode_str = self.training_manager.current_training_mode or "model"
    default_name = f"pile_model_{training_mode_str}_{current_time_str}"
    name_var = tk.StringVar(value=default_name)
    name_entry = tk.Entry(name_frame, textvariable=name_var, font=('Segoe UI', 9), width=50)
    name_entry.pack(fill='x', pady=(3,0))

    button_frame = tk.Frame(content_frame, bg='white')
    button_frame.pack(fill='x', pady=(15, 0)) # Adjusted padding

    def save_model_action(): # Renamed
        try:
            save_dir = path_var.get()
            file_name_base = name_var.get()
            if not file_name_base:
                messagebox.showerror("错误", "文件名不能为空。")
                return
            
            save_path = os.path.join(save_dir, file_name_base + ".pkl")
            os.makedirs(save_dir, exist_ok=True)

            # Use results_to_save which was determined at the start of show_save_options
            if results_to_save:
                model_to_save = None
                scaler_to_save = None
                
                # Try to get components from the training_system if available
                ts = self.training_manager.training_system
                if ts:
                    if hasattr(ts, 'model') and ts.model: model_to_save = ts.model
                    if hasattr(ts, 'scaler') and ts.scaler: scaler_to_save = ts.scaler
                    if hasattr(ts, 'analyzer'): # More specific checks for analyzer components
                        if hasattr(ts.analyzer, 'classifier_model') and ts.analyzer.classifier_model:
                            model_to_save = ts.analyzer.classifier_model
                        if hasattr(ts.analyzer, 'scaler') and ts.analyzer.scaler:
                            scaler_to_save = ts.analyzer.scaler
                
                # If model or scaler still None, try to get from enhanced_trainer
                et = self.training_manager.enhanced_trainer
                if et:
                    if model_to_save is None and hasattr(et, 'model') and et.model:
                        model_to_save = et.model
                    if scaler_to_save is None and hasattr(et, 'scaler') and et.scaler: # Assuming enhanced trainer might have a scaler
                        scaler_to_save = et.scaler


                # Fallback to simple model if nothing concrete found
                if model_to_save is None:
                    print("⚠️ 未找到具体模型组件，将创建并保存一个简单的占位符模型。")
                    from sklearn.ensemble import RandomForestClassifier
                    from sklearn.preprocessing import StandardScaler
                    model_to_save = RandomForestClassifier(n_estimators=10, random_state=42)
                    scaler_to_save = StandardScaler()
                    # Fit with dummy data for compatibility
                    X_dummy = np.random.rand(10, 5) 
                    y_dummy = np.random.randint(0, 2, 10)
                    scaler_to_save.fit(X_dummy)
                    model_to_save.fit(scaler_to_save.transform(X_dummy), y_dummy)


                compatible_model_data = {
                    'model': model_to_save,
                    'scaler': scaler_to_save,
                    'feature_extractor': getattr(ts, 'feature_extractor', None) if ts else None,
                    'feature_selector': getattr(ts, 'feature_selector', None) if ts else None,
                    'preprocessor': getattr(ts, 'preprocessor', None) if ts else None,
                    'training_mode': results_to_save.get('training_mode', self.training_manager.current_training_mode or 'unknown'),
                    'accuracy': results_to_save.get('accuracy', 0.0),
                    'metadata': {
                        'created_by': 'OptimizedTrainingGUI_v3.0',
                        'training_time_completed': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'source_script_version': '3.0' # Example version
                    }
                }
                # Add other components if they exist in training_system or enhanced_trainer
                if ts and hasattr(ts, 'analyzer'):
                    compatible_model_data['anomaly_detector'] = getattr(ts.analyzer, 'anomaly_detector', None)
                    compatible_model_data['feature_importance'] = getattr(ts.analyzer, 'feature_importance', None)
                
                import pickle
                with open(save_path, 'wb') as f:
                    pickle.dump(compatible_model_data, f)
                messagebox.showinfo("成功", f"模型已成功保存到:\n{save_path}\n\n此模型设计兼容 Pile_analyze_GZ_gui.py。")
                save_dialog.destroy()
            else:
                messagebox.showwarning("警告", "没有可保存的训练结果数据。")
        except Exception as e_save:
            messagebox.showerror("保存失败", f"保存模型时发生错误: {str(e_save)}")
            traceback.print_exc()

    tk.Button(button_frame, text="✔️ 保存", command=save_model_action,
             font=('Segoe UI', 10, 'bold'), bg=self.colors['success'],
             fg='white', padx=15, pady=5).pack(side='right', padx=(8, 0)) # Adjusted padding
    tk.Button(button_frame, text="❌ 取消", command=save_dialog.destroy, # Simpler cancel
             font=('Segoe UI', 10), bg='#6b7280',
             fg='white', padx=15, pady=5).pack(side='right')


def update_results_display(self, results_data): # Renamed
    """更新结果显示"""
    if not results_data:
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, "没有可显示的训练结果。")
        self.initialize_empty_plots() # Ensure plots are cleared
        return

    self.results_text.delete(1.0, tk.END)
    result_summary_text = self._generate_detailed_training_summary(results_data) # Renamed
    self.results_text.insert(tk.END, result_summary_text)
    self.update_training_plots(results_data)

def _generate_detailed_training_summary(self, results_dict): # Renamed
    """生成详细的训练结果摘要"""
    try:
        current_time_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S') # Renamed
        training_mode_str = self.training_manager.current_training_mode or "unknown" # Renamed

        summary_text = f"""
{'='*80}
🎯 AI桩基完整性分析模型训练结果摘要
{'='*80}

📅 报告生成时间: {current_time_str}
🚀 训练模式: {training_mode_str.upper()}
📊 系统版本: Advanced AI Pile Integrity Analysis System v3.0 (Optimized GUI)

{'='*80}
📈 模型性能指标
{'='*80}
"""

        if isinstance(results_dict, dict):
            accuracy_val = results_dict.get('accuracy', 'N/A') # Renamed
            if isinstance(accuracy_val, (int, float)):
                summary_text += f"🎯 总体准确率: {accuracy_val:.4f} ({accuracy_val*100:.2f}%)\n"
            else:
                summary_text += f"🎯 总体准确率: {accuracy_val}\n"

            metrics_list = [ # Renamed
                ('F1分数', 'f1_score'), ('精确率', 'precision'), ('召回率', 'recall'),
                ('AUC分数', 'auc_score'), ('交叉验证均分', 'cv_score_mean'), # Changed key for clarity
                ('交叉验证标准差', 'cv_score_std'), # Changed key
                ('训练准确率', 'train_accuracy'), ('验证准确率', 'val_accuracy')
            ]
            for metric_name, metric_key in metrics_list:
                value = results_dict.get(metric_key)
                if value is not None:
                    if isinstance(value, (int, float)):
                        if 'accuracy' in metric_key or 'score' in metric_key:
                            summary_text += f"📊 {metric_name}: {value:.4f} ({value*100:.2f}%)\n"
                        else:
                            summary_text += f"📊 {metric_name}: {value:.4f}\n"
                    else:
                        summary_text += f"📊 {metric_name}: {str(value)}\n" # Ensure string conversion

            summary_text += f"\n{'='*80}\n⚙️ 训练配置参数\n{'='*80}\n"
            # Display actual config used for training if available, else from GUI
            active_config = results_dict.get('config_used', self.config_vars) # Check if results_dict has config
            
            config_display_order = [ # Define order for display
                'epochs', 'batch_size', 'learning_rate', 'patience', 
                'dropout', 'ensemble_size', 'sequence_length', 'synthetic_samples'
            ]

            for key in config_display_order:
                value = None
                if isinstance(active_config, dict) and key in active_config: # If active_config is a dict (like from results)
                    value = active_config.get(key)
                elif hasattr(active_config, key) and key in self.config_vars : # If from self.config_vars (Tkinter vars)
                     value = self.config_vars[key].get()

                if value is not None:
                    summary_text += f"⚙️ {key.replace('_', ' ').title()}: {value}\n"


            summary_text += f"\n{'='*80}\n📊 训练数据信息\n{'='*80}\n"
            if self.training_manager.training_system:
                data_status_dict = self.training_manager.get_data_status() # Renamed
                total_files_count = sum(count for count in data_status_dict.values() if isinstance(count, int)) # Renamed
                summary_text += f"📁 总文件数: {total_files_count}\n"
                for class_name, count_val in data_status_dict.items():
                    if isinstance(count_val, int) and count_val > 0:
                        percentage = (count_val / total_files_count * 100) if total_files_count > 0 else 0
                        summary_text += f"📂 {class_name}: {count_val} 文件 ({percentage:.1f}%)\n"
            else:
                summary_text += "⚠️ 训练数据信息不可用 (训练系统未初始化)。\n"


            feature_count_val = results_dict.get('n_features', results_dict.get('feature_count', 'N/A')) # Renamed
            sample_count_val = results_dict.get('n_samples', results_dict.get('sample_count', 'N/A')) # Renamed
            if feature_count_val != 'N/A' or sample_count_val != 'N/A':
                summary_text += f"\n📊 特征维度: {feature_count_val}\n"
                summary_text += f"📊 样本数量: {sample_count_val}\n"

            summary_text += f"\n{'='*80}\n🤖 模型架构信息\n{'='*80}\n"
            model_type_str = results_dict.get('model_type', 'N/A') # Renamed
            summary_text += f"🧠 模型类型: {model_type_str}\n"
            if 'model_params' in results_dict and isinstance(results_dict['model_params'], dict):
                for param_name, param_value in results_dict['model_params'].items():
                    summary_text += f"🔧 {param_name}: {param_value}\n"
            
            training_time_val = results_dict.get('training_time') # Renamed
            if training_time_val: summary_text += f"\n⏱️ 训练耗时: {training_time_val}\n"
            
            # **FIXED PART for cv_scores**
            cv_scores_data = results_dict.get('cv_scores')
            if cv_scores_data is not None:
                is_valid_cv_scores = False
                if isinstance(cv_scores_data, np.ndarray) and cv_scores_data.size > 0:
                    is_valid_cv_scores = True
                elif isinstance(cv_scores_data, (list, tuple)) and len(cv_scores_data) > 0:
                    is_valid_cv_scores = True
                
                if is_valid_cv_scores:
                    summary_text += f"\n{'='*80}\n🔄 交叉验证详情 (各折叠分数)\n{'='*80}\n"
                    for i, score_val in enumerate(cv_scores_data, 1): # Renamed
                        summary_text += f"Fold {i}: {score_val:.4f} ({score_val*100:.2f}%)\n"
                    
                    # Calculate mean and std appropriately (already present in results_dict from some trainers)
                    mean_score_val = results_dict.get('cv_score_mean') # Prefer pre-calculated
                    std_score_val = results_dict.get('cv_score_std')   # Prefer pre-calculated

                    if mean_score_val is None: # Calculate if not found
                        if isinstance(cv_scores_data, np.ndarray):
                            mean_score_val = np.mean(cv_scores_data)
                            std_score_val = np.std(cv_scores_data)
                        else: # For list/tuple
                            mean_score_val = sum(cv_scores_data) / len(cv_scores_data)
                            std_score_val = (sum((x - mean_score_val)**2 for x in cv_scores_data) / len(cv_scores_data))**0.5
                    
                    if mean_score_val is not None:
                         summary_text += f"\n平均分数: {mean_score_val:.4f}"
                         if std_score_val is not None:
                              summary_text += f" ± {std_score_val:.4f}"
                         summary_text += "\n"


            if 'classification_report' in results_dict:
                summary_text += f"\n{'='*80}\n📋 分类报告\n{'='*80}\n{results_dict['classification_report']}\n"
            if 'confusion_matrix' in results_dict:
                summary_text += f"\n📊 混淆矩阵:\n{str(results_dict['confusion_matrix'])}\n" # Ensure string conversion

            # Feature importance (Top 10)
            feature_importance_data = results_dict.get('feature_importance') # Renamed
            if isinstance(feature_importance_data, dict) and feature_importance_data:
                summary_text += f"\n{'='*80}\n🔍 特征重要性 (Top 10)\n{'='*80}\n"
                sorted_features = sorted(feature_importance_data.items(), key=lambda x: x[1], reverse=True)[:10]
                for i, (feature_name, score_val) in enumerate(sorted_features, 1): # Renamed
                    summary_text += f"{i:2d}. {feature_name}: {score_val:.4f}\n"
            elif isinstance(feature_importance_data, list) and feature_importance_data: # Handle list of tuples
                summary_text += f"\n{'='*80}\n🔍 特征重要性 (Top 10)\n{'='*80}\n"
                # Assuming list of (name, score) tuples, already sorted or sortable
                # sorted_features = sorted(feature_importance_data, key=lambda x: x[1], reverse=True)[:10]
                for i, item in enumerate(feature_importance_data[:10], 1):
                    if isinstance(item, (list,tuple)) and len(item) == 2:
                         summary_text += f"{i:2d}. {item[0]}: {item[1]:.4f}\n"
                    else: # Fallback for other formats
                         summary_text += f"{i:2d}. {str(item)}\n"


            summary_text += f"\n{'='*80}\n💡 使用建议\n{'='*80}\n"
            if isinstance(accuracy_val, (int, float)):
                if accuracy_val >= 0.95: summary_text += "✅ 模型性能优秀，可考虑部署。\n"
                elif accuracy_val >= 0.90: summary_text += "✅ 模型性能良好，建议进一步验证。\n"
                elif accuracy_val >= 0.80: summary_text += "⚠️ 模型性能一般，可尝试优化。\n"
                else: summary_text += "❌ 模型性能较差，请检查数据或调整模型。\n"
            summary_text += "📝 定期使用新数据重训以保持性能。\n"
            summary_text += "🔍 实际应用中请结合传统方法验证。\n"

        else:
            summary_text += f"⚠️ 训练结果格式异常: {type(results_dict)}\n"
            summary_text += f"原始结果 (部分): {str(results_dict)[:500]}...\n"

        summary_text += f"\n{'='*80}\n"
        return summary_text

    except Exception as e_summary: # Renamed
        traceback.print_exc() # Print full traceback for debugging summary generation
        error_summary_text = f"""
{'='*80}
❌ 生成训练结果摘要时发生严重错误
{'='*80}

错误类型: {type(e_summary).__name__}
错误信息: {str(e_summary)}
训练结果类型: {type(results_dict)}
训练结果内容 (部分): {str(results_dict)[:500]}...

请检查控制台输出获取更详细的错误信息。
{'='*80}
"""
        return error_summary_text


def update_realtime_plots(self, message_data): # Renamed
    """实时更新训练图表"""
    if not hasattr(self, 'fig') or not hasattr(self, 'canvas_widget'): return

    epoch_num = message_data['epoch'] # Renamed
    # Add new data point or update existing one for the current epoch
    try:
        idx = self.realtime_training_data['epochs'].index(epoch_num)
        self.realtime_training_data['train_loss'][idx] = message_data['train_loss']
        self.realtime_training_data['val_loss'][idx] = message_data['val_loss']
        self.realtime_training_data['train_acc'][idx] = message_data['train_acc']
        self.realtime_training_data['val_acc'][idx] = message_data['val_acc']
    except ValueError: # Epoch not in list, append new data
        self.realtime_training_data['epochs'].append(epoch_num)
        self.realtime_training_data['train_loss'].append(message_data['train_loss'])
        self.realtime_training_data['val_loss'].append(message_data['val_loss'])
        self.realtime_training_data['train_acc'].append(message_data['train_acc'])
        self.realtime_training_data['val_acc'].append(message_data['val_acc'])

    self.fig.clear()
    ax1 = self.fig.add_subplot(121)
    ax2 = self.fig.add_subplot(122)
    epochs_list = self.realtime_training_data['epochs'] # Renamed

    if epochs_list: # Check if there's data to plot
        ax1.plot(epochs_list, self.realtime_training_data['train_loss'], 'b-', marker='o', markersize=3, label='训练损失') # Added markers
        ax1.plot(epochs_list, self.realtime_training_data['val_loss'], 'r-', marker='x', markersize=3, label='验证损失') # Added markers
        ax1.set_title('实时训练损失', fontsize=11, fontweight='bold') # Adjusted font
        ax1.set_xlabel('轮次 (Epoch)')
        ax1.set_ylabel('损失 (Loss)')
        ax1.legend(fontsize=8) # Adjusted font
        ax1.grid(True, linestyle='--', alpha=0.6) # Style grid

        ax2.plot(epochs_list, self.realtime_training_data['train_acc'], 'b-', marker='o', markersize=3, label='训练准确率')
        ax2.plot(epochs_list, self.realtime_training_data['val_acc'], 'r-', marker='x', markersize=3, label='验证准确率')
        ax2.set_title('实时训练准确率', fontsize=11, fontweight='bold')
        ax2.set_xlabel('轮次 (Epoch)')
        ax2.set_ylabel('准确率 (Accuracy)')
        ax2.legend(fontsize=8)
        ax2.grid(True, linestyle='--', alpha=0.6)
    
    self.fig.tight_layout(pad=2.0) # Add padding
    self.canvas_widget.draw_idle() # Use draw_idle for potentially better performance


def update_training_plots(self, results_data): # Renamed
    """更新训练图表"""
    if not hasattr(self, 'fig') or not results_data: return
    self.fig.clear()
    ax1 = self.fig.add_subplot(121)
    ax2 = self.fig.add_subplot(122)

    history_data = results_data.get('history') # Renamed
    if isinstance(history_data, dict):
        epochs_list = range(1, len(history_data.get('train_loss', [])) + 1) # Derive epochs if not present

        if 'train_loss' in history_data and 'val_loss' in history_data:
            ax1.plot(epochs_list, history_data['train_loss'], label='最终训练损失')
            ax1.plot(epochs_list, history_data['val_loss'], label='最终验证损失')
        ax1.set_title('训练损失曲线 (最终)', fontsize=11, fontweight='bold')
        ax1.set_xlabel('轮次')
        ax1.set_ylabel('损失')
        ax1.legend(fontsize=8)
        ax1.grid(True, linestyle='--', alpha=0.6)

        if 'train_acc' in history_data and 'val_acc' in history_data:
            ax2.plot(epochs_list, history_data['train_acc'], label='最终训练准确率')
            ax2.plot(epochs_list, history_data['val_acc'], label='最终验证准确率')
        ax2.set_title('训练准确率曲线 (最终)', fontsize=11, fontweight='bold')
        ax2.set_xlabel('轮次')
        ax2.set_ylabel('准确率')
        ax2.legend(fontsize=8)
        ax2.grid(True, linestyle='--', alpha=0.6)
    else: # If no history, fallback to realtime data if available or empty plot
        if self.realtime_training_data['epochs']: # If realtime data exists, plot that
             self.update_realtime_plots({'epoch':0, 'train_loss':0, 'val_loss':0, 'train_acc':0, 'val_acc':0}) # Dummy call to redraw
        else:
             self.initialize_empty_plots() # Show empty plots
             ax1.text(0.5, 0.4, '无历史数据', transform=ax1.transAxes, ha='center', va='center', fontsize=10, alpha=0.6)
             ax2.text(0.5, 0.4, '无历史数据', transform=ax2.transAxes, ha='center', va='center', fontsize=10, alpha=0.6)


    self.fig.tight_layout(pad=2.0)
    self.canvas_widget.draw_idle()

def initialize_empty_plots(self):
    """初始化空的训练图表"""
    if not hasattr(self, 'fig') or not hasattr(self, 'canvas_widget'): return
    self.fig.clear()
    ax1 = self.fig.add_subplot(121)
    ax2 = self.fig.add_subplot(122)

    ax1.set_title('训练损失曲线', fontsize=11, fontweight='bold')
    ax1.set_xlabel('轮次')
    ax1.set_ylabel('损失')
    ax1.grid(True, linestyle='--', alpha=0.6)
    ax1.text(0.5, 0.5, '等待训练数据...', transform=ax1.transAxes,
             ha='center', va='center', fontsize=12, alpha=0.7)

    ax2.set_title('训练准确率曲线', fontsize=11, fontweight='bold')
    ax2.set_xlabel('轮次')
    ax2.set_ylabel('准确率')
    ax2.grid(True, linestyle='--', alpha=0.6)
    ax2.text(0.5, 0.5, '等待训练数据...', transform=ax2.transAxes,
             ha='center', va='center', fontsize=12, alpha=0.7)

    self.fig.tight_layout(pad=2.0)
    self.canvas_widget.draw_idle()

def initialize_training_plots(self):
    """初始化训练图表（训练开始时）"""
    if not hasattr(self, 'fig') or not hasattr(self, 'canvas_widget'): return
    self.fig.clear()
    ax1 = self.fig.add_subplot(121)
    ax2 = self.fig.add_subplot(122)

    ax1.set_title('实时训练损失', fontsize=11, fontweight='bold')
    ax1.set_xlabel('轮次')
    ax1.set_ylabel('损失')
    ax1.grid(True, linestyle='--', alpha=0.6)
    ax1.text(0.5, 0.5, '训练启动中...\n等待首轮数据', transform=ax1.transAxes,
             ha='center', va='center', fontsize=11, alpha=0.8) # Adjusted text

    ax2.set_title('实时训练准确率', fontsize=11, fontweight='bold')
    ax2.set_xlabel('轮次')
    ax2.set_ylabel('准确率')
    ax2.grid(True, linestyle='--', alpha=0.6)
    ax2.text(0.5, 0.5, '训练启动中...\n等待首轮数据', transform=ax2.transAxes,
             ha='center', va='center', fontsize=11, alpha=0.8)

    self.fig.tight_layout(pad=2.0)
    self.canvas_widget.draw_idle()

# 将方法绑定到类
OptimizedTrainingGUI.create_data_tab = create_data_tab
OptimizedTrainingGUI.create_training_tab = create_training_tab
OptimizedTrainingGUI.create_results_tab = create_results_tab
OptimizedTrainingGUI.select_training_folder = select_training_folder
OptimizedTrainingGUI.update_data_status = update_data_status
OptimizedTrainingGUI.start_training = start_training
OptimizedTrainingGUI.progress_callback = progress_callback
OptimizedTrainingGUI.show_save_options = show_save_options
OptimizedTrainingGUI.update_results_display = update_results_display
OptimizedTrainingGUI._generate_detailed_training_summary = _generate_detailed_training_summary
OptimizedTrainingGUI.update_training_plots = update_training_plots
OptimizedTrainingGUI.update_realtime_plots = update_realtime_plots
OptimizedTrainingGUI.initialize_empty_plots = initialize_empty_plots
OptimizedTrainingGUI.initialize_training_plots = initialize_training_plots

# ==================== 主程序入口 ====================

def main():
    """主程序入口"""
    # Ensure auto_train_and_classify can be found if it's in a subfolder or needs path adjustment
    # current_dir = os.path.dirname(os.path.abspath(__file__))
    # # Example: if auto_train_and_classify.py is in a 'core' subdirectory
    # # core_path = os.path.join(current_dir, 'core') 
    # # if core_path not in sys.path:
    # #     sys.path.insert(0, core_path)
    
    # # Example: if auto_train_and_classify.py is one level up
    # # parent_dir = os.path.dirname(current_dir)
    # # if parent_dir not in sys.path:
    # #    sys.path.insert(0, parent_dir)


    try:
        app = OptimizedTrainingGUI()
        app.run()
    except Exception as e_main: # Renamed
        print(f"应用程序启动时发生严重错误: {e_main}")
        traceback.print_exc()
        # Fallback to a simple Tkinter error message if GUI cannot start
        try:
            root_error = tk.Tk()
            root_error.withdraw() # Hide the main window
            messagebox.showerror("严重错误", f"应用程序启动失败:\n{e_main}\n\n请查看控制台输出获取详细信息。")
            root_error.destroy()
        except Exception as e_tk_fallback:
             print(f"Tkinter fallback error message failed: {e_tk_fallback}")


if __name__ == "__main__":
    main()
